<template>
  <div class="option-main" id="app">
    <!-- 顶部标题栏 -->
    <div class="option-header" style="-webkit-app-region: drag;">
      <div class="logo">OfferHub</div>
      <div class="window-controls" style="-webkit-app-region: no-drag;">
        <button class="control-btn minimize-btn" @click="handleMinimize" title="最小化">
          <img src="/icons/zuixiaohua.png" alt="最小化" />
        </button>
        <button class="control-btn maximize-btn" @click="handleMaximize" title="最大化">
          <img src="/icons/zuidahua.png" alt="最大化" />
        </button>
        <button class="control-btn close-btn" @click="handleClose" title="关闭">
          <img src="/icons/guanbi.png" alt="关闭" />
        </button>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="option-body">
      <!-- 左侧选择栏 -->
      <div class="option-sidebar" :style="{ width: sidebarWidth + 'px' }">
        <ul class="sidebar-menu">
          <li class="menu-item" v-for="menuItem in menuItems" :key="menuItem.menuKey">
            <a href="#" 
               class="menu-link" 
               :class="{ 'active': currentMenuItem === menuItem.menuKey }"
               :data-menu="menuItem.menuKey" 
               @click.prevent="handleMenuClick(menuItem.menuKey)">
              <i v-if="menuItem.icon" :class="`icon-${menuItem.icon}`"></i>
              {{ menuItem.menuName }}
            </a>
          </li>
        </ul>
      </div>

      <!-- 可拖拽的分割条 -->
      <div class="sidebar-resizer" 
           @mousedown="startResize" 
           :style="{ left: sidebarWidth + 'px' }">
      </div>

      <!-- 右侧内容区域 -->
      <div class="option-content" :style="{ left: (sidebarWidth + 4) + 'px' }">
        <div class="content-body">
          <!-- 嵌套路由视图 -->
          <router-view />
        </div>
      </div>
    </div>
    
    <!-- 底部装饰栏 -->
    <div class="option-foot">
      <div class="foot-content">
        <span class="foot-text">OfferHub © 2025 - 专业职场助手</span>
        <div class="foot-links">
          <span class="foot-link">帮助中心</span>
          <span class="foot-divider">|</span>
          <span class="foot-link">隐私政策</span>
          <span class="foot-divider">|</span>
          <span class="foot-link">服务条款</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { WindowControlMixin, DraggableMixin, StorageMixin } from '../../mixins/mixins.js'
import { MenuControlMixin } from './option.js'

export default {
  name: 'Option',
  mixins: [WindowControlMixin, DraggableMixin, MenuControlMixin, StorageMixin],
  data() {
    return {
      currentMenuItem: 'interview', // 与路由默认重定向保持一致
      sidebarWidth: 250, // 默认sidebar宽度
      isResizing: false, // 是否正在调整大小
      minSidebarWidth: 150, // 最小宽度
      maxSidebarWidth: 400 // 最大宽度
    }
  },
  computed: {
    // 从路由配置中获取菜单项
    menuItems() {
      const optionRoute = this.$router.options.routes.find(route => route.name === 'Option')
      if (!optionRoute?.children) return []
      
      return optionRoute.children
        .filter(child => child.meta?.menuKey && child.meta?.menuName)
        .map(child => ({
          menuKey: child.meta.menuKey,
          menuName: child.meta.menuName,
          routeName: child.name,
          icon: child.meta.icon,
          order: child.meta.order || 999
        }))
        .sort((a, b) => a.order - b.order)
    }
  },
  mounted() {
    // 根据当前路由设置活跃菜单项
    this.updateActiveMenuItem()
    
    // 从localStorage恢复sidebar宽度 - 使用StorageMixin
    const savedWidth = this.loadFromStorage('optionSidebarWidth')
    if (savedWidth !== null) {
      const width = parseInt(savedWidth, 10)
      if (width >= this.minSidebarWidth && width <= this.maxSidebarWidth) {
        this.sidebarWidth = width
      }
    }
  },
  watch: {
    // 监听路由变化，更新活跃菜单项
    '$route'(to) {
      this.updateActiveMenuItem()
    }
  },
  methods: {
    // 处理菜单点击 - 使用命名路由导航
    handleMenuClick(menuKey) {
      const menuItem = this.menuItems.find(item => item.menuKey === menuKey)
      if (menuItem && this.$route.name !== menuItem.routeName) {
        this.$router.push({ name: menuItem.routeName })
      }
    },
    
    // 根据当前路由更新活跃菜单项
    updateActiveMenuItem() {
      // 使用路由的 meta.menuKey，如果没有则使用默认值
      this.currentMenuItem = this.$route.meta?.menuKey || 'interview'
    },

    // 开始调整sidebar宽度
    startResize(event) {
      this.isResizing = true
      document.addEventListener('mousemove', this.doResize)
      document.addEventListener('mouseup', this.stopResize)
      document.body.style.cursor = 'ew-resize'
      document.body.style.userSelect = 'none'
      event.preventDefault()
    },

    // 执行调整
    doResize(event) {
      if (!this.isResizing) return
      
      const newWidth = event.clientX
      if (newWidth >= this.minSidebarWidth && newWidth <= this.maxSidebarWidth) {
        this.sidebarWidth = newWidth
      }
    },

    // 停止调整
    stopResize() {
      this.isResizing = false
      document.removeEventListener('mousemove', this.doResize)
      document.removeEventListener('mouseup', this.stopResize)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
      
      // 保存到localStorage - 使用StorageMixin
      this.saveToStorage('optionSidebarWidth', this.sidebarWidth)
    }
  }
}
</script>

<style scoped>
@import 'option.scss';
</style>