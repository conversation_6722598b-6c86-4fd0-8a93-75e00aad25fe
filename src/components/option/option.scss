@use 'sass:color';

// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$gray: #a0a5a8;
$black: #181818;
$purple: #4B70E2;

*, *::after, *::before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  background-color: $neu-1;
  color: $gray;
  cursor: default;
}

#app {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: $neu-1;
  overflow: hidden;
  margin: 0;
  padding: 0;
  cursor: default;
}

.option-main {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: $neu-1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  cursor: default;
  
  // 移除整体拖动区域，只保留header可拖动
}

// 顶部标题栏 - 贴顶
.option-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background-color: $neu-1;
  border-bottom: 1px solid rgba($neu-2, 0.3);
  -webkit-app-region: drag;
  cursor: default;
  z-index: 100;
  margin: 0;
  
  .logo {
    font-size: 24px;
    font-weight: 700;
    color: $black;
    letter-spacing: 1px;
  }
  
  .window-controls {
    display: flex;
    gap: 10px;
    -webkit-app-region: no-drag;
    
    .control-btn {
      width: 30px;
      height: 30px;
      border: none;
      border-radius: 50%;
      background-color: $neu-1;
      color: $gray;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow:
        3px 3px 6px $neu-2,
        -3px -3px 6px $white;
      
      img {
        width: 16px;
        height: 16px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }
      
      &:hover {
        color: $black;
        box-shadow:
          5px 5px 8px $neu-2,
          -5px -5px 8px $white;
        transform: scale(1.05);
        
        img {
          opacity: 1;
        }
      }
      
      &:active {
        box-shadow:
          inset 2px 2px 4px $neu-2,
          inset -2px -2px 4px $white;
        transform: scale(0.95);
      }
      
      &.minimize-btn {
        &:hover {
          color: #f39c12;
        }
      }
      
      &.maximize-btn {
        &:hover {
          color: #27ae60;
        }
      }
      
      &.close-btn {
        &:hover {
          color: #e74c3c;
        }
      }
    }
  }
}

// 主体内容区域 - 为底部footer留出空间
.option-body {
  position: absolute;
  top: 60px; // header高度
  left: 0;
  right: 0;
  bottom: 40px; // footer高度
  width: 100%;
  height: calc(100% - 100px); // 总共减去header+footer的高度
  -webkit-app-region: no-drag;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

// 左侧选择栏 - 贴左，宽度由Vue动态控制
.option-sidebar {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  // width 由Vue的:style动态设置
  height: 100%;
  background-color: $neu-1;
  border-right: 1px solid rgba($neu-2, 0.3);
  padding: 20px;
  flex-shrink: 0; // 防止缩小
  overflow-y: auto; // 允许滚动
  margin: 0;
  
  .sidebar-menu {
    list-style: none;
    
    .menu-item {
      margin-bottom: 15px;
      
      .menu-link {
        display: block;
        padding: 15px 20px;
        text-decoration: none;
        color: $gray;
        font-size: 14px;
        font-weight: 500;
        border-radius: 12px;
        background-color: $neu-1;
        box-shadow:
          4px 4px 8px $neu-2,
          -4px -4px 8px $white;
        transition: all 0.3s ease;
        
        &:hover {
          color: $black;
          box-shadow:
            6px 6px 10px $neu-2,
            -6px -6px 10px $white;
          transform: translateY(-2px);
        }
        
        &:active,
        &.active {
          color: $purple;
          box-shadow:
            inset 3px 3px 6px $neu-2,
            inset -3px -3px 6px $white;
          transform: translateY(0);
        }
      }
    }
  }
}

// 可拖拽的分割条
.sidebar-resizer {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  // left 由Vue的:style动态设置
  background-color: transparent;
  cursor: ew-resize;
  z-index: 10;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba($purple, 0.3);
  }
  
  &:active {
    background-color: rgba($purple, 0.5);
  }
}

// 右侧内容区域 - 贴右并自适应拉伸，left由Vue动态控制
.option-content {
  position: absolute;
  // left 由Vue的:style动态设置
  top: 0;
  right: 0;
  bottom: 0;
  background-color: $neu-1;
  overflow-y: auto; // 允许滚动
  display: flex;
  flex-direction: column;
  margin: 0;
  min-width: 0; // 允许弹性收缩
  
  .content-body {
    flex: 1;
    background-color: $neu-1;
    padding: 30px;
    overflow-y: auto; // 允许内容滚动
    margin: 0;
    min-height: 0; // 允许内容区域收缩
  }
}

// 底部装饰栏 - 贴底并美观显示
.option-foot {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 40px;
  background-color: $neu-1;
  border-top: 1px solid rgba($neu-2, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  z-index: 10;
  
  .foot-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    padding: 0 30px;
    
    .foot-text {
      font-size: 12px;
      color: $black;
      font-weight: 600;
      text-shadow: none;
    }
    
    .foot-links {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .foot-link {
        font-size: 11px;
        color: $purple;
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
        
        &:hover {
          color: $black;
          background-color: rgba($purple, 0.1);
          text-shadow: none;
        }
      }
      
      .foot-divider {
        font-size: 11px;
        color: $gray;
        margin: 0 2px;
      }
    }
  }
  
  // 响应式处理
  @media (max-width: 768px) {
    .foot-content {
      flex-direction: column;
      gap: 5px;
      padding: 0 20px;
      
      .foot-text {
        font-size: 11px;
      }
      
      .foot-links {
        gap: 6px;
        
        .foot-link {
          font-size: 10px;
        }
      }
    }
  }
}

// 补充样式：确保所有滚动条样式一致
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: $neu-1;
}

::-webkit-scrollbar-thumb {
  background: $neu-2;
  border-radius: 4px;
  
  &:hover {
    background: color.adjust($neu-2, $lightness: -10%);
  }
}
