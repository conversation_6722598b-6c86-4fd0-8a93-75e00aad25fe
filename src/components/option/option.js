// Option组件的菜单控制混入
// 从mixins.js迁移过来，专门服务于Option组件

// 菜单控制混入
export const MenuControlMixin = {
  methods: {
    // 处理菜单点击
    handleMenuClick(menuItem) {
      console.log('菜单切换:', menuItem);
      this.currentMenuItem = menuItem;

      // 更新菜单激活状态
      this.$nextTick(() => {
        this.updateMenuActiveState(menuItem);
      });
    },

    // 更新菜单激活状态
    updateMenuActiveState(activeMenuItem) {
      // 移除所有菜单项的激活状态
      const menuLinks = document.querySelectorAll('.menu-link');
      menuLinks.forEach(link => {
        link.classList.remove('active');
      });

      // 添加当前菜单项的激活状态
      const activeLink = document.querySelector(`[data-menu="${activeMenuItem}"]`);
      if (activeLink) {
        activeLink.classList.add('active');
      }
    }
  }
};