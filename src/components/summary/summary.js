import interviewHistoryData from '@/assets/json/interview-history.json'

export default {
  name: 'Summary',
  data() {
    return {
      // 加载状态
      isGeneratingSummary: false, // 全局生成总结状态
      isCurrentGenerating: false, // 当前面试生成状态
      generatingProgress: 0, // 生成进度

      // 面试数据
      interviews: [],
      currentInterview: null, // 当前面试（从面试页面跳转过来的）
      selectedInterview: null, // 选中查看的面试
      

      
      // 弹窗状态
      showDetailsModal: false,
      
      // Toast 弹窗配置
      toastConfig: {
        show: false,
        type: 'success',
        title: '',
        message: '',
        icon: '/icons/chenggong.png'
      }
    }
  },
  

  
  mounted() {
    // 加载历史面试数据
    this.loadInterviewData();

    // 检查是否从面试页面跳转过来
    this.checkCurrentInterview();
  },
  
  methods: {
    // 加载面试数据
    async loadInterviewData() {
      try {
        // 直接加载历史面试数据
        this.interviews = interviewHistoryData.data.interviews;
        console.log('历史面试数据加载完成:', this.interviews);
      } catch (error) {
        console.error('加载面试数据失败:', error);
        this.showErrorToast('数据加载失败');
      }
    },
    
    // 检查当前面试（从面试页面跳转过来的）
    checkCurrentInterview() {
      // 检查路由参数或本地存储，看是否有当前面试数据
      const currentInterviewData = this.$route.query.current;
      if (currentInterviewData) {
        try {
          this.currentInterview = JSON.parse(decodeURIComponent(currentInterviewData));
          console.log('检测到当前面试数据:', this.currentInterview);

          // 开始生成总结
          this.startGeneratingSummary();
        } catch (error) {
          console.error('解析当前面试数据失败:', error);
        }
      }
    },

    // 开始生成总结
    async startGeneratingSummary() {
      this.isCurrentGenerating = true;
      this.generatingProgress = 0;

      // 模拟生成进度
      const progressInterval = setInterval(() => {
        this.generatingProgress += Math.random() * 15 + 5; // 每次增加5-20%
        if (this.generatingProgress >= 100) {
          this.generatingProgress = 100;
          clearInterval(progressInterval);

          // 3秒后完成生成
          setTimeout(() => {
            this.isCurrentGenerating = false;
            this.showSuccessToast('总结生成完成', '面试总结已生成，可以查看详情');
          }, 500);
        }
      }, 200);
    },
    
    // 选择面试
    selectInterview(interview) {
      this.selectedInterview = interview;
      console.log('选中面试:', interview);
    },
    
    // 查看详情
    viewDetails(interview) {
      this.selectedInterview = interview;
      this.showDetailsModal = true;
    },
    
    // 关闭详情弹窗
    closeDetailsModal() {
      this.showDetailsModal = false;
    },
    
    // 导出报告
    exportReport(interview) {
      console.log('导出面试报告:', interview);
      this.showSuccessToast('报告导出中...', '正在生成PDF报告，请稍候');
      
      // 模拟导出过程
      setTimeout(() => {
        this.showSuccessToast('导出成功', '面试报告已保存到下载文件夹');
      }, 2000);
    },
    
    // 格式化日期
    formatDate(dateString) {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    // 格式化日期时间
    formatDateTime(dateString, timeString) {
      const date = this.formatDate(dateString);
      const time = timeString.substring(0, 5); // 只取小时:分钟
      return `${date} ${time}`;
    },

    // 格式化日期时间范围
    formatDateTimeRange(dateString, startTime, endTime) {
      const date = this.formatDate(dateString);
      const start = startTime.substring(0, 5); // 只取小时:分钟
      const end = endTime.substring(0, 5); // 只取小时:分钟
      return `${date} ${start}---${end}`;
    },

    // 格式化日期时间范围
    formatDateTimeRange(dateString, startTime, endTime) {
      const date = this.formatDate(dateString);
      const start = startTime.substring(0, 5); // 只取小时:分钟
      const end = endTime.substring(0, 5); // 只取小时:分钟
      return `${date} ${start}---${end}`;
    },
    
    // 获取评分样式类
    getScoreClass(score) {
      if (score >= 90) return 'excellent';
      if (score >= 80) return 'good';
      if (score >= 70) return 'average';
      return 'poor';
    },



    // 获取表现等级样式类
    getPerformanceClass(performance) {
      const classMap = {
        '优秀': 'excellent',
        '良好': 'good',
        '一般': 'average',
        '较差': 'poor'
      };
      return classMap[performance] || 'average';
    },
    
    // Toast 弹窗方法
    showToast(type, title, message = '') {
      this.toastConfig = {
        show: true,
        type,
        title,
        message,
        icon: this.getToastIcon(type)
      };
      
      // 3秒后自动隐藏
      setTimeout(() => {
        this.toastConfig.show = false;
      }, 3000);
    },
    
    showSuccessToast(title, message = '') {
      this.showToast('success', title, message);
    },
    
    showErrorToast(title, message = '') {
      this.showToast('error', title, message);
    },
    
    showWarningToast(title, message = '') {
      this.showToast('warning', title, message);
    },
    
    showInfoToast(title, message = '') {
      this.showToast('info', title, message);
    },
    
    getToastIcon(type) {
      const icons = {
        success: '/icons/chenggong.png',
        error: '/icons/shibai.png',
        warning: '/icons/zhuyi.png',
        info: '/icons/xinxi.png'
      };
      return icons[type] || icons.info;
    }
  }
}
