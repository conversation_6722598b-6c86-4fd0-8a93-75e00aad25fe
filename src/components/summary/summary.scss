// 面试总结页面样式
// 变量定义（与其他组件保持一致）
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$black: #181818;
$purple: #4B70E2;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-muted: #94a3b8;
$success-color: #10b981;
$info-color: #3b82f6;
$warning-color: #f59e0b;
$error-color: #ef4444;
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

// 主容器
.summary-container {
  width: 100%;
  height: 100%;
  background-color: $neu-1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $neu-1;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  
  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid $neu-2;
    border-top: 4px solid $purple;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }
  
  .loading-text {
    color: $text-secondary;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .loading-subtitle {
    color: $text-muted;
    font-size: 14px;
    margin: 0;
  }
}

// 主要内容
.summary-main {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $neu-1;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $neu-2;
    border-radius: 3px;
  }
}

// 页面标题
.summary-header {
  margin-bottom: 32px;
  
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: $text-primary;
    margin: 0 0 8px 0;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: $text-secondary;
    margin: 0;
  }
}

// 区域标题
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: $text-primary;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .section-actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 面试徽章
.interview-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;

  &.current {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
  }

  &.generating {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    animation: pulse 2s infinite;
  }
}



// 当前面试区域
.current-interview-section {
  margin-bottom: 40px;
}

// 面试卡片
.interview-card {
  background-color: $white;
  border-radius: 16px;
  padding: 18px; /* 调整padding */
  margin-bottom: 16px;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 更平滑的过渡 */
  cursor: pointer;
  position: relative; /* 为右上角评分定位 */
  min-height: 120px; /* 设置基础最小高度 */
  overflow: hidden; /* 防止动画时内容溢出 */

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      12px 12px 24px $neu-2,
      -12px -12px 24px $white;
  }

  &.selected {
    border: 2px solid $purple;
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white,
      0 0 0 3px rgba(75, 112, 226, 0.1);
    padding: 20px; /* selected状态下增加padding */
    min-height: 240px; /* selected状态下更大的最小高度 */
  }

  &.generating {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(217, 119, 6, 0.05));
    border: 1px solid rgba(245, 158, 11, 0.2);
  }
}

// 右上角评分
.score-corner {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
  opacity: 0;
  transform: scale(0.8) translateY(20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  .interview-card.selected & {
    opacity: 1;
    transform: scale(1) translateY(0);
    transition-delay: 0.2s; /* 延迟出现，让卡片先展开 */
  }

  .score-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 700;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    &.excellent {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    &.good {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    &.average {
      background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    &.poor {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }
  }
}

// 面试内容容器
.interview-content {
  width: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

// 生成中内容
.generating-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 0;

  .generating-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(245, 158, 11, 0.2);
    border-top: 4px solid #f59e0b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    flex-shrink: 0;
  }

  .generating-info {
    flex: 1;

    .interview-title {
      font-size: 18px;
      font-weight: 600;
      color: $text-primary;
      margin: 0 0 8px 0;
    }

    .generating-text {
      font-size: 14px;
      color: $text-secondary;
      margin: 0 0 16px 0;
    }

    .generating-progress {
      height: 6px;
      background-color: rgba(245, 158, 11, 0.1);
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #f59e0b, #d97706);
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 12px;
      color: $text-muted;
      margin: 0;
    }
  }
}

// 面试信息
.interview-info {
  .interview-title {
    font-size: 17px;
    font-weight: 600;
    color: $text-primary;
    margin: 0 0 8px 0;
    line-height: 1.3;
    transition: all 0.3s ease;
  }

  .interview-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;

    span {
      font-size: 13px;
      color: $text-secondary;
      transition: color 0.3s ease;
    }
  }
}

// 未选中状态下的面试信息
.interview-card:not(.selected) .interview-info {
  margin-bottom: 16px;
}

  // 选中状态下的详情
  .selected-details {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid $neu-2;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards;

    .highlights-improvements {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;

      .highlights-section,
      .improvements-section {
        .section-title {
          font-size: 13px;
          color: $text-secondary;
          font-weight: 600;
          margin: 0 0 8px 0;
        }

        .highlight-list,
        .improvement-list {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            font-size: 12px;
            color: $text-primary;
            padding: 4px 0;
            position: relative;
            padding-left: 16px;
            line-height: 1.4;
            opacity: 0;
            transform: translateX(-10px);
            animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;

            // 为每个列表项添加递增的延迟
            @for $i from 1 through 10 {
              &:nth-child(#{$i}) {
                animation-delay: #{0.5 + $i * 0.1}s;
              }
            }

            &:before {
              content: '•';
              position: absolute;
              left: 0;
              font-weight: bold;
            }
          }
        }
      }

      .highlights-section .highlight-list li:before {
        color: $success-color;
      }

      .improvements-section .improvement-list li:before {
        color: $warning-color;
      }
    }
  }

// 面试统计
.interview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
  
  .stat-item {
    text-align: center;
    padding: 16px;
    background-color: $neu-1;
    border-radius: 12px;
    
    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: $text-primary;
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 12px;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

// 面试总结（右侧）
.interview-summary {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  .interview-card.selected & {
    opacity: 0;
    transform: translateY(-20px);
    pointer-events: none;
  }

  .score-display {
    text-align: center;
    flex-shrink: 0;

    .score-circle {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 700;
      color: white;
      margin: 0 auto 6px;

      &.excellent {
        background: linear-gradient(135deg, #10b981, #059669);
      }

      &.good {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      }

      &.average {
        background: linear-gradient(135deg, #f59e0b, #d97706);
      }

      &.poor {
        background: linear-gradient(135deg, #ef4444, #dc2626);
      }
    }

    .score-label {
      font-size: 11px;
      color: $text-secondary;
      font-weight: 500;
    }
  }

  .quick-stats {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    .quick-stat {
      text-align: center;
      min-width: 60px;

      .stat-number {
        display: block;
        font-size: 15px;
        font-weight: 700;
        color: $text-primary;
        line-height: 1.2;
      }

      .stat-text {
        font-size: 11px;
        color: $text-secondary;
        margin-top: 2px;
      }
    }
  }
}

// 操作按钮
.interview-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  opacity: 0;
  transform: translateY(10px);
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.8s forwards;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover:before {
    left: 100%;
  }

  &.small {
    padding: 8px 16px;
    font-size: 13px;
  }

  &.primary {
    background: $purple;
    color: white;

    &:hover {
      transform: translateY(-2px) scale(1.02);
      box-shadow: 0 8px 20px rgba(75, 112, 226, 0.4);
    }

    &:active {
      transform: translateY(-1px) scale(1.01);
    }
  }

  &.secondary {
    background-color: $neu-1;
    color: $text-primary;
    border: 1px solid $neu-2;

    &:hover {
      transform: translateY(-2px) scale(1.02);
      background-color: rgba(209, 217, 230, 0.4);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(-1px) scale(1.01);
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    width: 80px;
    height: 80px;
    opacity: 0.5;
    margin-bottom: 20px;
  }
  
  .empty-text {
    font-size: 16px;
    color: $text-muted;
    margin: 0;
  }
}

// 历史面试列表
.history-section {
  .interview-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

// 弹窗样式
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

.popup-dialog {
  background-color: $white;
  border-radius: 16px;
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.1),
    -20px -20px 40px rgba(255, 255, 255, 0.8);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;

  .popup-overlay.show & {
    transform: scale(1);
  }

  &.details-modal {
    width: 800px;
    max-width: 90vw;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px; /* 增加底部padding */
  min-height: 60px; /* 设置最小高度 */

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: $text-primary;
    margin: 0;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    font-size: 24px;
    color: $text-secondary;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background-color: $neu-1;
      color: $text-primary;
    }
  }
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: $neu-1;
  }

  &::-webkit-scrollbar-thumb {
    background: $neu-2;
    border-radius: 3px;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center; /* 垂直居中 */
  gap: 12px;
  padding: 16px 24px 24px; /* 增加顶部padding */
  min-height: 60px; /* 设置最小高度 */

  .modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &.primary {
      background: $purple;
      color: white;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 14px rgba(75, 112, 226, 0.35);
      }
    }

    &.secondary {
      background-color: $neu-1;
      color: $text-primary;
      border: 1px solid $neu-2;

      &:hover {
        transform: translateY(-1px);
        background-color: rgba(209, 217, 230, 0.25);
      }
    }
  }
}

// 面试详情
.interview-details {
  .detail-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: $text-primary;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid $neu-1;
    }
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .detail-label {
        font-size: 12px;
        color: $text-secondary;
        font-weight: 500;
      }

      .detail-value {
        font-size: 14px;
        color: $text-primary;
        font-weight: 600;
      }
    }
  }

  .performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;

    .performance-item {
      text-align: center;
      padding: 16px;
      background-color: $neu-1;
      border-radius: 12px;

      .performance-label {
        font-size: 12px;
        color: $text-secondary;
        margin-bottom: 8px;
      }

      .performance-value {
        font-size: 20px;
        font-weight: 700;
        color: $text-primary;

        &.score {
          color: $purple;
        }
      }
    }
  }

  .highlights-list,
  .improvements-list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 12px 16px;
      margin-bottom: 8px;
      background-color: $neu-1;
      border-radius: 8px;
      font-size: 14px;
      color: $text-primary;
      position: relative;
      padding-left: 40px;

      &:before {
        content: '•';
        position: absolute;
        left: 16px;
        color: $purple;
        font-weight: bold;
        font-size: 16px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .improvements-list li:before {
    color: $warning-color;
  }



  // 面试流程
  .interview-flow {
    .flow-item {
      margin-bottom: 20px;
      padding: 16px;
      background-color: $neu-1;
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .flow-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      .phase-name {
        font-size: 14px;
        font-weight: 600;
        color: $text-primary;
      }

      .phase-duration {
        font-size: 12px;
        color: $text-muted;
        background-color: rgba(100, 116, 139, 0.1);
        padding: 2px 8px;
        border-radius: 4px;
      }

      .phase-performance {
        font-size: 12px;
        font-weight: 600;
        padding: 2px 8px;
        border-radius: 4px;

        &.excellent {
          background-color: rgba(16, 185, 129, 0.1);
          color: $success-color;
        }

        &.good {
          background-color: rgba(59, 130, 246, 0.1);
          color: $info-color;
        }

        &.average {
          background-color: rgba(245, 158, 11, 0.1);
          color: $warning-color;
        }

        &.poor {
          background-color: rgba(239, 68, 68, 0.1);
          color: $error-color;
        }
      }
    }

    .phase-notes {
      font-size: 13px;
      color: $text-secondary;
      margin: 0;
      line-height: 1.5;
    }
  }

  // 关键问题
  .key-questions {
    .question-item {
      margin-bottom: 20px;
      padding: 16px;
      background-color: $neu-1;
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .question-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
      gap: 12px;

      .question-text {
        font-size: 14px;
        font-weight: 600;
        color: $text-primary;
        flex: 1;
      }

      .question-score {
        font-size: 14px;
        font-weight: 700;
        color: $purple;
        white-space: nowrap;
      }
    }

    .answer-text {
      font-size: 13px;
      color: $text-secondary;
      margin: 0;
      line-height: 1.5;
    }
  }
}

// 动画
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .summary-main {
    padding: 16px;
  }

  .interview-card {
    padding: 16px;
  }

  .interview-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .interview-summary {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .quick-stats {
    justify-content: space-around;
    width: 100%;
  }

  .interview-actions {
    justify-content: center;
  }

  .popup-dialog.details-modal {
    width: 95vw;
    margin: 20px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .performance-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
