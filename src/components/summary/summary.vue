<template>
  <div class="summary-container">
    <!-- 生成总结加载状态 -->
    <div v-if="isGeneratingSummary" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">生成总结中...</p>
        <p class="loading-subtitle">正在分析面试内容，请稍候</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else class="summary-main">
      <!-- 页面标题 -->
      <div class="summary-header">
        <h2 class="page-title">面试总结</h2>
        <p class="page-subtitle">查看和管理您的历史面试记录</p>
      </div>

      <!-- 当前面试总结（如果有） -->
      <div v-if="currentInterview" class="current-interview-section">
        <div class="section-header">
          <h3 class="section-title">当前面试总结</h3>
          <div class="interview-badge" :class="{ current: !isCurrentGenerating, generating: isCurrentGenerating }">
            {{ isCurrentGenerating ? '生成中' : '刚刚完成' }}
          </div>
        </div>
        <div class="interview-card selected" :class="{ generating: isCurrentGenerating }">
          <!-- 生成中状态 -->
          <div v-if="isCurrentGenerating" class="generating-content">
            <div class="generating-spinner"></div>
            <div class="generating-info">
              <h4 class="interview-title">{{ currentInterview.title }}</h4>
              <p class="generating-text">正在分析面试内容，生成详细总结...</p>
              <div class="generating-progress">
                <div class="progress-bar" :style="{ width: generatingProgress + '%' }"></div>
              </div>
              <p class="progress-text">{{ generatingProgress }}% 完成</p>
            </div>
          </div>

          <!-- 完成状态 -->
          <div v-else>
            <div class="interview-info">
              <h4 class="interview-title">{{ currentInterview.title }}</h4>
              <div class="interview-meta">
                <span class="duration">{{ currentInterview.duration }}</span>
              </div>
            </div>
            <div class="interview-stats">
              <div class="stat-item">
                <div class="stat-value">{{ currentInterview.summary.transcriptWordCount }}</div>
                <div class="stat-label">转写字数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ currentInterview.summary.questionCount }}</div>
                <div class="stat-label">问题数量</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ currentInterview.summary.suggestionCount }}</div>
                <div class="stat-label">建议生成</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ currentInterview.score }}</div>
                <div class="stat-label">综合评分</div>
              </div>
            </div>
            <div class="interview-actions">
              <button class="action-btn primary" @click="viewDetails(currentInterview)">
                查看详情
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 历史面试列表 -->
      <div class="history-section">
        <div class="section-header">
          <h3 class="section-title">历史面试记录</h3>
        </div>

        <div v-if="interviews.length === 0" class="empty-state">
          <img src="/icons/wenjian.png" alt="暂无数据" class="empty-icon" />
          <p class="empty-text">暂无历史面试记录</p>
        </div>

        <div v-else class="interview-list">
          <div
            v-for="interview in interviews"
            :key="interview.id"
            class="interview-card"
            :class="{ selected: selectedInterview?.id === interview.id }"
            @click="selectInterview(interview)"
          >
            <!-- 综合评分（右上角） -->
            <div v-if="selectedInterview?.id === interview.id" class="score-corner">
              <div class="score-circle" :class="getScoreClass(interview.score)">
                {{ interview.score }}
              </div>
            </div>

            <div class="interview-content">
              <div class="interview-info">
                <h4 class="interview-title">{{ interview.title }}</h4>
                <div class="interview-meta">
                  <span class="date">{{ formatDateTimeRange(interview.date, interview.startTime, interview.endTime) }}</span>
                </div>

                <!-- 选中状态下显示的额外详情 -->
                <div v-if="selectedInterview?.id === interview.id" class="selected-details">
                  <div class="highlights-improvements">
                    <div class="highlights-section">
                      <h5 class="section-title">面试亮点</h5>
                      <ul class="highlight-list">
                        <li v-for="(highlight, index) in interview.highlights" :key="index">
                          {{ highlight }}
                        </li>
                      </ul>
                    </div>
                    <div class="improvements-section">
                      <h5 class="section-title">改进建议</h5>
                      <ul class="improvement-list">
                        <li v-for="(improvement, index) in interview.improvements" :key="index">
                          {{ improvement }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 未选中状态下显示的统计信息 -->
              <div v-if="selectedInterview?.id !== interview.id" class="interview-summary">
                <div class="score-display">
                  <div class="score-circle" :class="getScoreClass(interview.score)">
                    {{ interview.score }}
                  </div>
                  <span class="score-label">综合评分</span>
                </div>
                <div class="quick-stats">
                  <div class="quick-stat">
                    <span class="stat-number">{{ interview.summary.transcriptWordCount }}</span>
                    <span class="stat-text">转写字数</span>
                  </div>
                  <div class="quick-stat">
                    <span class="stat-number">{{ interview.summary.questionCount }}</span>
                    <span class="stat-text">问题</span>
                  </div>
                  <div class="quick-stat">
                    <span class="stat-number">{{ interview.summary.suggestionCount }}</span>
                    <span class="stat-text">建议</span>
                  </div>
                  <div class="quick-stat">
                    <span class="stat-number">{{ interview.duration }}</span>
                    <span class="stat-text">时长</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="interview-actions">
              <button class="action-btn small primary" @click.stop="viewDetails(interview)">
                查看详情
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetailsModal" class="popup-overlay show" @click="closeDetailsModal">
      <div class="popup-dialog details-modal" @click.stop>
        <div class="modal-header">
          <h3>面试详情</h3>
          <button class="close-btn" @click="closeDetailsModal">×</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedInterview" class="interview-details">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="detail-label">面试时间</span>
                  <span class="detail-value">{{ formatDateTimeRange(selectedInterview.date, selectedInterview.startTime, selectedInterview.endTime) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">面试时长</span>
                  <span class="detail-value">{{ selectedInterview.duration }}</span>
                </div>
              </div>
            </div>
            
            <div class="detail-section">
              <h4>面试表现</h4>
              <div class="performance-grid">
                <div class="performance-item">
                  <div class="performance-label">转写字数</div>
                  <div class="performance-value">{{ selectedInterview.summary.transcriptWordCount }}</div>
                </div>
                <div class="performance-item">
                  <div class="performance-label">问题数量</div>
                  <div class="performance-value">{{ selectedInterview.summary.questionCount }}</div>
                </div>
                <div class="performance-item">
                  <div class="performance-label">建议生成</div>
                  <div class="performance-value">{{ selectedInterview.summary.suggestionCount }}</div>
                </div>
                <div class="performance-item">
                  <div class="performance-label">综合评分</div>
                  <div class="performance-value score">{{ selectedInterview.score }}</div>
                </div>
              </div>
            </div>



            <!-- 面试流程 -->
            <div v-if="selectedInterview.interviewFlow" class="detail-section">
              <h4>面试流程</h4>
              <div class="interview-flow">
                <div v-for="(phase, index) in selectedInterview.interviewFlow" :key="index" class="flow-item">
                  <div class="flow-header">
                    <span class="phase-name">{{ phase.phase }}</span>
                    <span class="phase-duration">{{ phase.duration }}</span>
                    <span class="phase-performance" :class="getPerformanceClass(phase.performance)">
                      {{ phase.performance }}
                    </span>
                  </div>
                  <p class="phase-notes">{{ phase.notes }}</p>
                </div>
              </div>
            </div>

            <!-- 关键问题 -->
            <div v-if="selectedInterview.keyQuestions" class="detail-section">
              <h4>关键问题回答</h4>
              <div class="key-questions">
                <div v-for="(qa, index) in selectedInterview.keyQuestions" :key="index" class="question-item">
                  <div class="question-header">
                    <span class="question-text">{{ qa.question }}</span>
                    <span class="question-score">{{ qa.score }}/10</span>
                  </div>
                  <p class="answer-text">{{ qa.answer }}</p>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4>面试亮点</h4>
              <ul class="highlights-list">
                <li v-for="highlight in selectedInterview.highlights" :key="highlight">
                  {{ highlight }}
                </li>
              </ul>
            </div>

            <div class="detail-section">
              <h4>改进建议</h4>
              <ul class="improvements-list">
                <li v-for="improvement in selectedInterview.improvements" :key="improvement">
                  {{ improvement }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="modal-btn secondary" @click="closeDetailsModal">关闭</button>
          <button class="modal-btn primary" @click="exportReport(selectedInterview)">导出报告</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./summary.js"></script>
<style lang="scss" scoped src="./summary.scss"></style>
