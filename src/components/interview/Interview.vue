<template>
  <div class="interview-container">
    <!-- 顶部状态栏 -->
    <div class="interview-header">
      <div class="header-left">
        <button 
          class="start-interview-btn"
          :class="{ recording: isRecording }"
          @click="toggleRecording"
        >
          {{ isRecording ? '停止面试' : '开始面试' }}
        </button>
        <div class="status-indicator">
          <div class="status-dot" :class="{ active: isRecording }"></div>
          <span class="status-text">{{ isRecording ? '正在录音' : '待机中' }}</span>
        </div>
      </div>
      <div class="header-right">
        <span class="session-time">{{ sessionTime }}</span>
        <span class="remaining-quota">剩余次数: {{ remainingQuota }}</span>
        <button class="settings-btn" @click="showSettings = !showSettings" title="面试设置">
          <img src="/icons/shezhi.png" alt="设置" />
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="interview-main" :class="{ 'vertical-layout': layoutMode === 'vertical' }">
        <!-- 实时转写区域 -->
        <div class="transcript-section">
          <div class="section-header">
            <h3 class="section-title">实时转写</h3>
            <div class="transcript-controls">
              <div class="audio-source-controls">
                <button class="control-btn microphone-source-btn" @click="showMicrophoneSourceModal = true" title="选择麦克风音频源">
                  我: {{ getSelectedMicrophoneSource().name }}
                </button>
                <button class="control-btn software-source-btn" @click="showSoftwareSourceModal = true" title="选择软件音频源">
                  面试官: {{ getSelectedSoftwareSource().name }}
                </button>
              </div>
            </div>
          </div>
          <div class="transcript-content" ref="transcriptContent">
            <div v-if="transcript.length === 0" class="empty-state">
              <img src="/icons/dianhua.png" alt="麦克风" class="empty-icon" />
              <p>点击"开始面试"开始语音转写</p>
            </div>
            <div v-else class="transcript-list">
              <div 
                v-for="(item, index) in transcript" 
                :key="index"
                class="transcript-item"
                :class="item.type"
                @mousedown="handleSelectionStart"
                @selectstart="handleSelectionStart"
              >
                <div class="transcript-meta">
                  <span class="speaker">{{ item.speaker }}</span>
                  <span class="timestamp">{{ item.timestamp }}</span>
                </div>
                <div class="transcript-text selectable-text">{{ item.text }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 智能建议区域 -->
        <div class="suggestions-section">
          <div class="section-header">
            <h3 class="section-title">
              智能建议
              <span v-if="isGeneratingSuggestion" class="loading-indicator">生成中...</span>
            </h3>
            <div class="suggestion-controls">
              
              <button class="icon-btn" @click="regenerateSuggestions" title="重新生成">
                <img src="/icons/shuaxin.png" alt="重新生成" />
              </button>
            </div>
          </div>
          <div class="suggestions-content">
            <div v-if="suggestions.length === 0" class="empty-state">
              <img src="/icons/shezhi.png" alt="聊天" class="empty-icon" />
              <p>AI将根据面试官的问题生成回答建议</p>
            </div>
            <div v-else class="suggestions-list">
              <div 
                v-for="(suggestion, index) in suggestions" 
                :key="index"
                class="suggestion-item"
                @mousedown="handleSelectionStart"
                @selectstart="handleSelectionStart"
              >
                <div class="suggestion-header">
                  <span class="suggestion-label">建议 {{ index + 1 }}</span>
                </div>
                <div class="suggestion-content">
                  <div class="suggestion-text selectable-text">{{ suggestion.content }}</div>
                  <div v-if="suggestion.references" class="suggestion-references">
                    <strong>参考要点：</strong>
                    <ul>
                      <li v-for="ref in suggestion.references" :key="ref" class="selectable-text">{{ ref }}</li>
                    </ul>
                  </div>
                </div>
                <div class="suggestion-footer">
                  <span class="confidence">置信度: {{ suggestion.confidence }}%</span>
                  <span class="generation-time">{{ suggestion.generatedAt }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>

    <!-- 设置面板 -->
    <div v-if="showSettings" class="settings-panel">
      <div class="settings-content">
        <div class="settings-header">
          <h3>面试设置</h3>
          <button class="close-btn" @click="showSettings = false">
            <img src="/icons/guanbi.png" alt="关闭" />
          </button>
        </div>
        <div class="settings-body">
          <div class="setting-group">
            <h4>基础设置</h4>
            <div class="setting-item">
              <label>语言模式</label>
              <SelectBox
                v-model="languageMode"
                :options="languageOptions"
                placeholder="选择语言"
                custom-class="setting-select"
              />
            </div>
          </div>
          <div class="setting-group">
            <h4>界面布局</h4>
            <div class="setting-item">
              <label>转写与建议布局</label>
              <div class="layout-options">
                <label class="radio-option">
                  <input type="radio" v-model="layoutMode" value="horizontal" />
                  <span>左右结构</span>
                </label>
                <label class="radio-option">
                  <input type="radio" v-model="layoutMode" value="vertical" />
                  <span>上下结构</span>
                </label>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 麦克风音频源选择模态框 -->
    <div v-if="showMicrophoneSourceModal" class="popup-overlay modal-overlay show" @click="closeMicrophoneSourceModal">
      <div class="popup-dialog modal-content audio-source-modal" @click.stop>
        <div class="modal-header">
          <h3>选择麦克风音频源（面试者）</h3>
          <button class="modal-close" @click="closeMicrophoneSourceModal">
            <img src="/icons/guanbi.png" alt="关闭" />
          </button>
        </div>
        <div class="modal-body">
          <div class="audio-source-description">
            <p>选择用于录制您语音的麦克风设备</p>
          </div>
          <div class="audio-sources">
            <div 
              v-for="source in microphoneSources" 
              :key="source.id"
              class="audio-source-item"
              :class="{ active: selectedMicrophoneSource === source.id }"
              @click="() => { selectMicrophoneSource(source.id); closeMicrophoneSourceModal(); showToast('success','音频源切换成功', `麦克风已切换到: ${getSelectedMicrophoneSource().name}`); }"
            >
              <div class="source-icon" :class="source.iconClass"></div>
              <div class="source-info">
                <div class="source-name">{{ source.name }}</div>
                <div class="source-app">{{ source.app }}</div>
                <div class="source-description">{{ source.description }}</div>
              </div>
              <div class="source-level">
                <div class="level-bars">
                  <div class="level-bar" :class="{ active: source.level >= 1 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 2 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 3 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 4 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 5 }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 软件音频源选择模态框 -->
    <div v-if="showSoftwareSourceModal" class="popup-overlay modal-overlay show" @click="closeSoftwareSourceModal">
      <div class="popup-dialog modal-content audio-source-modal" @click.stop>
        <div class="modal-header">
          <h3>选择软件音频源（面试官）</h3>
          <button class="modal-close" @click="closeSoftwareSourceModal">
            <img src="/icons/guanbi.png" alt="关闭" />
          </button>
        </div>
        <div class="modal-body">
          <div class="audio-source-description">
            <p>选择用于录制面试官语音的软件音频输出</p>
          </div>
          <div class="audio-sources">
            <div 
              v-for="source in softwareAudioSources" 
              :key="source.id"
              class="audio-source-item"
              :class="{ active: selectedSoftwareSource === source.id }"
              @click="() => { selectSoftwareSource(source.id); closeSoftwareSourceModal(); showToast('success','音频源切换成功', `软件音频已切换到: ${getSelectedSoftwareSource().name}`); }"
            >
              <div class="source-icon" :class="source.iconClass"></div>
              <div class="source-info">
                <div class="source-name">{{ source.name }}</div>
                <div class="source-app">{{ source.app }}</div>
                <div class="source-description">{{ source.description }}</div>
              </div>
              <div class="source-level">
                <div class="level-bars">
                  <div class="level-bar" :class="{ active: source.level >= 1 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 2 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 3 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 4 }"></div>
                  <div class="level-bar" :class="{ active: source.level >= 5 }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="interview-footer">
      <!-- 已将统计与操作迁移至结束弹窗，此处隐藏 -->
      <div class="footer-stats" v-if="false"></div>
      <div class="footer-actions" v-if="false"></div>
    </div>

    <!-- 伦理声明弹窗 -->
    <div v-if="showEthicsModal" class="popup-overlay modal-overlay show">
      <div class="popup-dialog modal-content ethics-modal" @click.stop>
        <div class="modal-header">
          <h3>使用声明</h3>
        </div>
        <div class="modal-body">
          <div class="ethics-content">
            <div class="ethics-icon">
              <img src="/icons/zhuyi.png" alt="警告" class="ethics-warning-icon" />
            </div>
            <h4>重要提醒</h4>
            <p>本工具旨在提升面试能力，建议仅用于模拟练习。正式求职中使用可能违反用人单位规定。</p>
            <ul class="ethics-list">
              <li>请诚实面对每一次面试机会</li>
              <li>本工具仅作为学习和练习辅助</li>
              <li>建议在模拟环境中使用以提升能力</li>
              <li>使用时请遵守相关法律法规</li>
            </ul>
          </div>
        </div>
        <div class="modal-footer">
          <button class="modal-btn primary" @click="agreeAndStart">同意并继续</button>
        </div>
      </div>
    </div>

    <!-- 面试确认弹窗 -->
    <div v-if="showInterviewConfirmModal" class="popup-overlay show" @click="closeInterviewConfirmModal">
      <div class="popup-dialog" @click.stop>
        <div class="popup-icon info"></div>
        <h3 class="popup-title">确认开始面试</h3>
        <div class="popup-message">
          <p>• 每次面试将消耗 <strong>1次</strong> 使用次数</p>
          <p>• 单次面试时长限制为 <strong>30分钟</strong></p>
          <p>• 30分钟后如账户余额充足将自动续费继续</p>
        </div>
        <div class="interview-position-input">
          <label class="position-label">面试职位</label>
          <input
            type="text"
            v-model="interviewPosition"
            placeholder="请输入面试职位，如：前端开发工程师"
            class="position-input"
            maxlength="50"
          />
        </div>
        <div class="interview-confirm-options">
          <label class="auto-renewal-checkbox">
            <input
              type="checkbox"
              v-model="autoRenewalEnabled"
              @change="saveAutoRenewalSetting"
            />
            <span class="checkbox-text">30分钟后自动续费</span>
          </label>
        </div>
        <div class="popup-buttons">
          <button class="popup-btn secondary" @click="closeInterviewConfirmModal">取消</button>
          <button class="popup-btn primary" @click="confirmStartInterview">开始面试</button>
        </div>
      </div>
    </div>

    <!-- 离开面试确认对话框 -->
    <div v-if="showInterviewExitConfirm" class="popup-overlay show">
      <div class="popup-dialog confirm-dialog" @click.stop>
        <div class="popup-icon warning"></div>
        <h3 class="popup-title">离开页面确认</h3>
        <p class="popup-message">您正在进行面试，离开页面将结束当前面试会话
          <br/>
          确定要离开吗？</p>
        <div class="popup-buttons">
          <button class="popup-btn secondary" @click="cancelInterviewExit">继续面试</button>
          <button class="popup-btn warning" @click="confirmInterviewExit">确定离开</button>
        </div>
      </div>
    </div>

    <!-- 面试结束总结弹窗（与开始面试弹窗统一样式与尺寸） -->
    <div class="popup-overlay" :class="{ show: showInterviewSummaryModal }" @click="closeInterviewSummaryModal">
      <div class="popup-dialog" @click.stop>
        <div class="popup-icon info"></div>
        <h3 class="popup-title">面试结束总结</h3>
        <div class="popup-message">
          <div class="summary-grid plain">
            <div class="summary-item">
              <div class="summary-label">面试时长</div>
              <div class="summary-value">{{ sessionTime }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">转写字数</div>
              <div class="summary-value">{{ transcriptWordCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">问题数量</div>
              <div class="summary-value">{{ questionCount }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">建议生成</div>
              <div class="summary-value">{{ suggestionCount }}</div>
            </div>
          </div>
        </div>
        <div class="popup-buttons">
          <button class="popup-btn secondary" @click="closeInterviewSummaryModal">关闭</button>
          <button class="popup-btn primary" @click="goToSummary">查看总结</button>
        </div>
      </div>
    </div>

    <!-- Toast 弹窗容器 -->
    <div class="toast-container" :class="{ show: toastConfig.show }">
      <div class="toast" :class="toastConfig.type">
        <div class="toast-icon">
          <img :src="toastConfig.icon" :alt="toastConfig.type" />
        </div>
        <div class="toast-content">
          <div class="toast-title">{{ toastConfig.title }}</div>
          <div class="toast-message">{{ toastConfig.message }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ToastMixin, TimerMixin, ModalMixin, StorageMixin } from '../../mixins/mixins.js'
import InterviewComponent from './interview.js'
import SelectBox from '../../communal/selectBox.vue'

export default {
  ...InterviewComponent,
  components: {
    SelectBox
  },
  mixins: [ToastMixin, TimerMixin, ModalMixin, StorageMixin],
  data() {
    return {
      ...InterviewComponent.data(),
      languageOptions: [
        { value: 'mixed', label: '中英混合' },
        { value: 'zh-CN', label: '中文' },
        { value: 'en-US', label: 'English' }
      ]
    }
  },
  mounted() {
    InterviewComponent.mounted.call(this);
  },
  beforeUnmount() {
    InterviewComponent.beforeUnmount.call(this);
  },
  methods: {
    ...InterviewComponent.methods
  },
  watch: {
    ...InterviewComponent.watch
  }
}
</script>
<style scoped>
@import './interview.scss';
</style>
<style>
@import '../../communal/popupPoge.scss';
@import '../../communal/popupSmooth.scss';
</style>