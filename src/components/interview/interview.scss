.popup-dialog .summary-grid.plain {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  text-align: left;
}

.popup-dialog .summary-grid.plain .summary-item {
  background: transparent;
  border: none;
  padding: 4px 0;
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.popup-dialog .summary-grid.plain .summary-label {
  font-size: 13px;
  color: #64748b;
}

.popup-dialog .summary-grid.plain .summary-value {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
}
.summary-popup { display: none; }

.summary-popup .popup-title { display: none; }

.summary-popup .popup-message { display: none; }

.summary-grid.plain {
  display: grid;
  grid-template-columns: 1fr; /* 单列，拉长横向 */
  gap: 16px; /* 增加间距 */
  margin: 20px 0; /* 增加上下边距 */
}

.summary-grid.plain .summary-item {
  background: transparent; /* 全白底，内部信息无边框 */
  border: none;
  border-radius: 0;
  padding: 16px 8px; /* 增加内边距 */
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.summary-grid.plain .summary-label {
  font-size: 15px; /* 增大标签字体 */
  color: #64748b; // 原 $text-secondary
  font-weight: 500; /* 增加字重 */
}

.summary-grid.plain .summary-value {
  font-size: 26px; /* 增大数值字体 */
  font-weight: 700;
  color: #1e293b; // 原 $text-primary
  letter-spacing: 0.5px; /* 增加字间距 */
}

.summary-popup .popup-actions { display: none; }
// 面试辅助页面样式
// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$black: #181818;
$purple: #4B70E2;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-muted: #94a3b8;
$success-color: #10b981;
$info-color: #3b82f6;
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

// 主容器
.interview-container {
  width: 100%;
  height: 100%;
  background-color: $neu-1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 顶部状态栏
.interview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, $purple 0%, #667eea 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .start-interview-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      
      .btn-icon {
        width: 14px;
        height: 14px;
        object-fit: contain;
      }
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }
      
      &.recording {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        animation: recordingPulse 2s infinite;
      }
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        
        &.active {
          background-color: #10b981;
          box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
          animation: pulse 2s infinite;
        }
      }
      
      .status-text {
        font-weight: 600;
        font-size: 16px;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .session-time {
      font-family: 'Courier New', monospace;
      font-size: 18px;
      font-weight: 700;
    }
    
    .remaining-quota {
      padding: 4px 12px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      font-size: 14px;
      font-weight: 500;
    }
    
    .settings-btn {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      
      img {
        width: 18px;
        height: 18px;
        opacity: 0.9;
        transition: opacity 0.2s ease;
      }
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
        
        img {
          opacity: 1;
        }
      }
    }
  }
}

// 主要内容区域
.interview-main {
  flex: 1;
  display: flex;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
  
  &.vertical-layout {
    flex-direction: column;
    
    .transcript-section,
    .suggestions-section {
      flex: 1;
      min-height: 300px;
    }
  }
}

// 通用区域样式
.transcript-section,
.suggestions-section {
  flex: 1;
  background-color: $neu-1;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 区域标题
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 700;
  color: $black;
  margin-bottom: 20px;
  
  .title-icon {
    width: 18px;
    height: 18px;
    object-fit: contain;
    flex-shrink: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .loading-indicator {
    color: $purple;
    font-size: 14px;
    font-weight: 500;
    
    /* loading indicator styles can be added here if needed */
  }
}

// 音频源选择
.audio-sources {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .audio-source-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 2px solid transparent;
    border-radius: 12px;
    background-color: $neu-1;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
      4px 4px 8px $neu-2,
      -4px -4px 8px $white;
    
    &:hover {
      border-color: rgba($purple, 0.3);
      transform: translateY(-2px);
      box-shadow: 
        6px 6px 12px $neu-2,
        -6px -6px 12px $white;
    }
    
    &.active {
      border-color: $purple;
      background: linear-gradient(135deg, rgba($purple, 0.1) 0%, rgba($info-color, 0.05) 100%);
    }
    
    .source-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, rgba($purple, 0.2) 0%, rgba($info-color, 0.1) 100%);
      color: $purple;
      font-size: 24px;
    }
    
    .source-info {
      flex: 1;
      
      .source-name {
        font-size: 16px;
        font-weight: 600;
        color: $black;
        margin-bottom: 4px;
      }
      
      .source-app {
        font-size: 14px;
        color: $text-secondary;
      }
    }
    
    .source-level {
      .level-bars {
        display: flex;
        gap: 2px;
        
        .level-bar {
          width: 4px;
          height: 16px;
          border-radius: 2px;
          background-color: $neu-2;
          transition: background-color 0.3s ease;
          
          &.active {
            background-color: $success-color;
          }
        }
      }
    }
  }
}

// 控制按钮
.control-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  background-color: $neu-1;
  color: $purple;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 
    4px 4px 8px $neu-2,
    -4px -4px 8px $white;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 
      6px 6px 12px $neu-2,
      -6px -6px 12px $white;
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 
      inset 4px 4px 8px $neu-2,
      inset -4px -4px 8px $white;
  }
  
  .btn-icon {
    width: 14px;
    height: 14px;
    object-fit: contain;
  }
}

.transcript-controls,
.suggestion-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

// 音频源控件组
.audio-source-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

// 音频源按钮样式
.microphone-source-btn,
.software-source-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background-color: $neu-1;
  color: $text-primary;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 
    2px 2px 4px $neu-2,
    -2px -2px 4px $white;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 
      3px 3px 6px $neu-2,
      -3px -3px 6px $white;
  }
}

.microphone-source-btn {
  border-left: 3px solid $success-color;
}

.software-source-btn {
  border-left: 3px solid $info-color;
}



.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background-color: $neu-1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 
    3px 3px 6px $neu-2,
    -3px -3px 6px $white;
  
  img {
    width: 16px;
    height: 16px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 
      4px 4px 8px $neu-2,
      -4px -4px 8px $white;
    
    img {
      opacity: 1;
    }
  }
}

// 转写内容
.transcript-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba($purple, 0.3);
    border-radius: 3px;
  }
}

.transcript-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.transcript-item {
  padding: 16px;
  border-radius: 12px;
  background-color: $neu-1;
  box-shadow: 
    4px 4px 8px $neu-2,
    -4px -4px 8px $white;
  
  // 隔离文本选择，防止跨条目拖选
  isolation: isolate;
  contain: style;
  
  &.interviewer {
    background: linear-gradient(135deg, rgba($purple, 0.1) 0%, rgba($info-color, 0.05) 100%);
    border-left: 4px solid $purple;
  }
  
  &.candidate {
    background: linear-gradient(135deg, rgba($success-color, 0.1) 0%, rgba($success-color, 0.05) 100%);
    border-left: 4px solid $success-color;
  }
  
  .transcript-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .speaker {
      font-weight: 600;
      color: $black;
    }
    
    .timestamp {
      font-size: 12px;
      color: $text-secondary;
      font-family: 'Courier New', monospace;
      margin-left: auto; // 确保时间靠右显示
    }
  }
  
  .transcript-text {
    color: $text-primary;
    line-height: 1.5;
  }
}

// 可选中文本样式
.selectable-text {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
  
  // 防止选择跨越到其他元素
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  
  // 限制选中范围在当前元素内
  &::selection {
    background-color: rgba(75, 112, 226, 0.2);
    color: inherit;
  }
  
  &::-moz-selection {
    background-color: rgba(75, 112, 226, 0.2);
    color: inherit;
  }
}

// 为转写和建议的容器添加选择边界
.transcript-item,
.suggestion-item {
  // 创建新的块格式化上下文，防止文本选择跨越
  display: block;
  overflow: hidden;
  
  // 强制隔离文本选择
  &::before {
    content: '';
    display: block;
    height: 0;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
  
  &::after {
    content: '';
    display: block;
    height: 0;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
}

// 建议内容
.suggestions-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  background-color: $neu-1;
  border-radius: 12px;
  box-shadow: 
    4px 4px 8px $neu-2,
    -4px -4px 8px $white;
  
  // 隔离文本选择，防止跨建议条目拖选
  isolation: isolate;
  contain: style;
  
  .suggestion-header {
    padding: 16px 16px 0;
    
    .suggestion-label {
      font-weight: 600;
      color: $purple;
    }
  }
  
  .suggestion-content {
    padding: 0 16px 16px;
    
    .suggestion-text {
      color: $text-primary;
      line-height: 1.6;
      margin-bottom: 12px;
    }
    
    .suggestion-references {
      font-size: 12px;
      color: $text-muted;
      font-style: italic;
      
      strong {
        color: $text-secondary;
      }
    }
  }
  
  .suggestion-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 16px;
    
    .confidence {
      font-size: 12px;
      color: $text-secondary;
      font-weight: 500;
    }
    
    .generation-time {
      font-size: 11px;
      color: $text-muted;
      font-family: 'Courier New', monospace;
    }
  }
}



// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: $text-muted;
  text-align: center;
  
  .empty-icon {
    width: 36px;
    height: 36px;
    margin-bottom: 16px;
    opacity: 0.5;
    object-fit: contain;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 设置面板
.settings-panel {
  position: absolute;
  top: 70px;
  right: 24px;
  width: 320px;
  background-color: $neu-1;
  border-radius: 16px;
  box-shadow: 
    12px 12px 24px $neu-2,
    -12px -12px 24px $white;
  z-index: 1000;
  overflow: hidden;
  
  .settings-content {
    .settings-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      background: linear-gradient(135deg, $purple 0%, #667eea 100%);
      color: white;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .close-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        
        img {
          width: 16px;
          height: 16px;
          opacity: 0.8;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
          
          img {
            opacity: 1;
          }
        }
      }
    }
    
    .settings-body {
      padding: 24px;
      
      .setting-group {
        margin-bottom: 24px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h4 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: $black;
        }
      }
      
      .setting-item {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
          color: $text-primary;
        }
        
        .setting-description {
          margin: 6px 0 0 0;
          font-size: 12px;
          color: $text-muted;
          line-height: 1.4;
        }
      }
      
      .setting-select {
        width: 100%;
        padding: 8px 12px;
        border: 2px solid transparent;
        border-radius: 8px;
        background-color: $neu-1;
        color: $text-primary;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 
          inset 2px 2px 4px $neu-2,
          inset -2px -2px 4px $white;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        
        &:focus {
          outline: none;
          border-color: $purple;
        }
      }
      
      .layout-options {
        display: flex;
        gap: 12px;
        
        .radio-option {
          flex: 1;
          display: flex !important;
          align-items: center;
          justify-content: center;
          padding: 8px;
          border: 2px solid transparent;
          border-radius: 8px;
          background-color: $neu-1;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 
            4px 4px 8px $neu-2,
            -4px -4px 8px $white;
          
          input {
            display: none;
          }
          
          span {
            font-size: 14px;
            font-weight: 500;
            color: $text-primary;
          }
          
          &:has(input:checked) {
            border-color: $purple;
            box-shadow: 
              inset 2px 2px 4px $neu-2,
              inset -2px -2px 4px $white;
            
            span {
              color: $purple;
            }
          }
        }
      }
      
      .checkbox-option {
        display: flex !important;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-radius: 8px;
        background-color: $neu-1;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 
          4px 4px 8px $neu-2,
          -4px -4px 8px $white;
        
        input {
          width: 18px;
          height: 18px;
          margin: 0;
        }
        
        span {
          font-size: 14px;
          color: $text-primary;
        }
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 
            6px 6px 12px $neu-2,
            -6px -6px 12px $white;
        }
      }
    }
  }
}

// 模态框
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.modal-content {
  // 样式已统一到 popupPoge.scss 的 .popup-dialog，仅保留尺寸与配色修饰
  max-width: 90vw;
  max-height: 90vh;
  background-color: $neu-1; // 与页面统一的浅灰背景
  
  &.audio-source-modal {
    width: 500px;
  }
  
  &.summary-modal { display: none; }
  
  &.ethics-modal {
    width: 520px;
    max-width: 90vw;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: $neu-1;
    color: $text-primary;
    border-bottom: 1px solid rgba(209, 217, 230, 0.6);
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      letter-spacing: 0.2px;
    }
    
    .modal-close {
      width: 32px;
      height: 32px;
      border: 1px solid rgba(209, 217, 230, 0.8);
      border-radius: 8px;
      background: $neu-1;
      color: $text-primary;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      padding: 0;
      
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        display: block;
      }
      
      &:hover {
        background: rgba(102, 126, 234, 0.06);
        border-color: rgba(102, 126, 234, 0.4);
      }
    }
  }
  
  .modal-body {
    padding: 24px;
    
    .summary-info { display: none; }
    
    .audio-source-description {
      padding: 16px;
      background: rgba(75, 112, 226, 0.1);
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid $purple;
      
      p {
        margin: 0;
        color: $text-secondary;
        font-size: 14px;
        line-height: 1.5;
      }
    }
    
    .audio-sources {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .audio-source-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border-radius: 12px;
      background-color: $neu-1;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      box-shadow: 
        4px 4px 8px $neu-2,
        -4px -4px 8px $white;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 
          6px 6px 12px $neu-2,
          -6px -6px 12px $white;
      }
      
      &.active {
        border-color: $purple;
        background: linear-gradient(135deg, rgba(75, 112, 226, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
        box-shadow: 
          inset 2px 2px 4px rgba(75, 112, 226, 0.2),
          4px 4px 8px $neu-2,
          -4px -4px 8px $white;
      }
      
      .source-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: $gradient-primary;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        flex-shrink: 0;
        
        &.icon-microphone::before {
          content: "🎤";
        }
        
        &.icon-microphone-usb::before {
          content: "🎧";
        }
        
        &.icon-headset::before {
          content: "🎧";
        }
        
        &.icon-speaker::before {
          content: "🔊";
        }
        
        &.icon-wechat::before {
          content: "💬";
        }
        
        &.icon-video::before {
          content: "📹";
        }
      }
      
      .source-info {
        flex: 1;
        
        .source-name {
          font-size: 16px;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 4px;
        }
        
        .source-app {
          font-size: 13px;
          color: $text-secondary;
          margin-bottom: 6px;
        }
        
        .source-description {
          font-size: 12px;
          color: $text-muted;
          line-height: 1.4;
        }
      }
      
      .source-level {
        .level-bars {
          display: flex;
          gap: 2px;
          align-items: flex-end;
          
          .level-bar {
            width: 4px;
            height: 8px;
            background-color: $neu-2;
            border-radius: 2px;
            transition: all 0.3s ease;
            
            &:nth-child(2) { height: 12px; }
            &:nth-child(3) { height: 16px; }
            &:nth-child(4) { height: 20px; }
            &:nth-child(5) { height: 24px; }
            
            &.active {
              background: linear-gradient(to top, $success-color 0%, #34d399 100%);
            }
          }
        }
      }
    }
    
    .ethics-content {
      text-align: left;
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .ethics-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .ethics-warning-icon {
          width: 36px;
          height: 36px;
          object-fit: contain;
          filter: brightness(0) invert(1);
        }
      }
      
      h4 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: $black;
      }
      
      p {
        margin: 0;
        color: $text-primary;
        line-height: 1.6;
      }
      
      .ethics-list {
        margin: 0;
        padding-left: 18px;
        
        li {
          margin: 6px 0;
          color: $text-primary;
          line-height: 1.5;
        }
      }
    }
  }
  
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    background: $neu-1;
    border-top: 1px solid rgba(209, 217, 230, 0.6);
    
    .modal-btn {
      padding: 10px 20px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.primary {
        background: #4B70E2;
        color: white;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 14px rgba(75, 112, 226, 0.35);
        }
      }
      
      &.secondary {
        background-color: $neu-1;
        color: $text-primary;
        border: 1px solid rgba(209, 217, 230, 0.6);
        
        &:hover {
          transform: translateY(-1px);
          background-color: rgba(209, 217, 230, 0.25);
          border-color: rgba(209, 217, 230, 0.6);
        }
      }
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes recordingPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计


// 底部工具栏样式（转写字数/问题数量/建议生成 + 操作按钮）
.interview-footer {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 12px 0;
}

.footer-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.stat-item {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  border-radius: 10px;
  background-color: $neu-1;
  box-shadow:
    4px 4px 8px $neu-2,
    -4px -4px 8px $white;
}

.stat-label {
  color: $text-secondary;
  font-size: 12px;
}

.stat-value {
  color: $purple;
  font-weight: 700;
  font-size: 14px;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  color: white;
  font-size: 14px;
  font-weight: 700;
  white-space: nowrap;
  background: $gradient-primary;
  box-shadow:
    6px 6px 12px $neu-2,
    -6px -6px 12px $white;
  transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;

  .btn-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white;
  }

  &:active {
    transform: translateY(0);
  }
}

.footer-actions .footer-btn:nth-child(2) {
  background: $gradient-secondary;
}

@media (max-width: 768px) {
  .interview-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  .footer-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

// 面试确认弹窗专用样式
.interview-confirm-options {
  margin: 20px 0;
  text-align: left;
}

.auto-renewal-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  user-select: none;
  
  input[type="checkbox"] {
    width: 20px;
    height: 20px;
    appearance: none;
    border: 2px solid $neu-2;
    border-radius: 4px;
    background: $neu-1;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    box-shadow: 
      inset 2px 2px 4px $neu-2,
      inset -2px -2px 4px $white;
    
    &:checked {
      background: $gradient-primary;
      border-color: transparent;
      box-shadow: 
        2px 2px 4px $neu-2,
        -2px -2px 4px $white;
      
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 10px;
        border: 2px solid white;
        border-left: none;
        border-top: none;
        transform: translate(-50%, -60%) rotate(45deg);
      }
    }
    
    &:hover {
      box-shadow: 
        inset 3px 3px 6px $neu-2,
        inset -3px -3px 6px $white;
    }
  }
  
  .checkbox-text {
    font-size: 16px;
    color: $text-secondary;
    font-weight: 500;
  }
}

// 面试职位输入框
.interview-position-input {
  margin: 20px 0;

  .position-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 8px;
  }

  .position-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid $neu-2;
    border-radius: 8px;
    background-color: $white;
    color: $text-primary;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &::placeholder {
      color: $text-muted;
    }

    &:focus {
      outline: none;
      border-color: $purple;
      box-shadow: 0 0 0 3px rgba(75, 112, 226, 0.1);
    }

    &:hover {
      border-color: rgba(75, 112, 226, 0.3);
    }
  }
}

.popup-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 10px;
}

// 弹窗消息样式增强
.popup-message {
  p {
    margin: 8px 0;
    text-align: left;
    
    strong {
      color: $purple;
      font-weight: 700;
    }
  }
}

