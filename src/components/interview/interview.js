export default {
  name: 'Interview',
  data() {
    return {
      // 录音状态
      isRecording: false,
      sessionTime: '00:00:00',
      remainingQuota: 15,
      
      // 双音频源配置（始终开启）
      selectedMicrophoneSource: 'default-microphone',
      selectedSoftwareSource: 'system-audio',
      
      // 麦克风音频源（面试者）
      microphoneSources: [
        {
          id: 'default-microphone',
          name: '默认麦克风',
          app: '系统默认',
          iconClass: 'icon-microphone',
          level: 4,
          role: 'candidate',
          description: '系统默认麦克风设备，用于录制面试者语音'
        },
        {
          id: 'usb-microphone',
          name: 'USB麦克风',
          app: '外接设备',
          iconClass: 'icon-microphone-usb',
          level: 5,
          role: 'candidate',
          description: '外接USB麦克风，提供更清晰的音质'
        },
        {
          id: 'headset-microphone',
          name: '耳机麦克风',
          app: '耳机设备',
          iconClass: 'icon-headset',
          level: 4,
          role: 'candidate',
          description: '耳机内置麦克风，适合近距离录音'
        }
      ],
      
      // 软件音频源（面试官）
      softwareAudioSources: [
        {
          id: 'system-audio',
          name: '系统音频',
          app: '系统扬声器',
          iconClass: 'icon-speaker',
          level: 3,
          role: 'interviewer',
          description: '系统扬声器输出，录制面试官通过软件播放的语音'
        },
        {
          id: 'wechat-audio',
          name: '微信音频',
          app: '微信',
          iconClass: 'icon-wechat',
          level: 4,
          role: 'interviewer',
          description: '微信应用音频输出，录制微信通话中面试官的语音'
        },
        {
          id: 'tencent-meeting-audio',
          name: '腾讯会议音频',
          app: '腾讯会议',
          iconClass: 'icon-video',
          level: 5,
          role: 'interviewer',
          description: '腾讯会议应用音频，录制会议中面试官的语音'
        },
        {
          id: 'zoom-audio',
          name: 'Zoom会议音频',
          app: 'Zoom',
          iconClass: 'icon-video',
          level: 0,
          role: 'interviewer',
          description: 'Zoom会议应用音频，录制Zoom会议中面试官的语音'
        },
        {
          id: 'dingtalk-audio',
          name: '钉钉会议音频',
          app: '钉钉',
          iconClass: 'icon-video',
          level: 2,
          role: 'interviewer',
          description: '钉钉应用音频，录制钉钉会议中面试官的语音'
        }
      ],
      
      // 设置
      languageMode: 'mixed', // 默认为中英混合
      
      // 转写记录
      transcript: [
        {
          type: 'interviewer',
          speaker: '面试官',
          timestamp: '14:32:15',
          text: '请先自我介绍一下，包括你的教育背景和工作经历。',
          audioSource: 'software',
          sourceDevice: '系统音频'
        },
        {
          type: 'candidate',
          speaker: '我',
          timestamp: '14:32:45',
          text: '好的，我叫张三，毕业于清华大学计算机科学与技术专业...',
          audioSource: 'microphone',
          sourceDevice: '默认麦克风'
        }
      ],
      
      // 智能建议
      isGeneratingSuggestion: false,
      suggestions: [
        {
          content: '建议从教育背景开始，重点突出与岗位相关的专业技能和项目经验。可以按照"教育背景-工作经历-核心技能-职业规划"的结构来组织回答。',
          references: [
            '突出计算机专业背景的优势',
            '结合具体项目案例说明技术能力',
            '展现学习能力和解决问题的思维'
          ],
          confidence: 92,
          generatedAt: '14:32:50',

        },
        {
          content: '在介绍工作经历时，建议使用STAR法则（情况-任务-行动-结果）来描述具体成就，量化你的贡献和影响。',
          references: [
            '用数据说话，展现工作成果',
            '强调团队协作和领导能力',
            '体现持续学习和自我提升'
          ],
          confidence: 88,
          generatedAt: '14:32:52',

        }
      ],
      
      // 统计数据
      transcriptWordCount: 156,
      questionCount: 3,
      suggestionCount: 6,
      
      // 模态框
      showEthicsModal: true,
      showSettings: false,
      showMicrophoneSourceModal: false,
      showSoftwareSourceModal: false,
      showInterviewConfirmModal: false,
      showInterviewSummaryModal: false,
      
      // 面试确认弹窗相关
      autoRenewalEnabled: false,
      interviewPosition: '', // 面试职位
      
      // 路由切换确认对话框
      showInterviewExitConfirm: false,
      pendingRoute: null, // 存储待跳转的路由
      
      // 布局设置
      layoutMode: 'horizontal', // horizontal 或 vertical
      
      // 定时器已由TimerMixin管理
      startTime: null,
      
      // Toast 弹窗配置
      toastConfig: {
        show: false,
        type: 'success', // success, error, warning, info
        title: '',
        message: '',
        icon: '/icons/chenggong.png'
      },
      
      // 文本选择控制（无需持久状态）
    }
  },
  
  computed: {
    // 检查是否正在进行面试
    isInterviewInProgress() {
      return this.isRecording;
    }
  },
  
  beforeRouteLeave(to, from, next) {
    // 检查是否正在进行面试
    if (this.isInterviewInProgress) {
      // 存储待跳转的路由和next函数
      this.pendingRoute = { to, from, next };
      // 显示自定义确认对话框
      this.showInterviewExitConfirm = true;
    } else {
      // 没有进行面试，直接离开
      next();
    }
  },
  
  mounted() {
    // 初始化音频源检测
    this.detectAudioSources();
    
    // 模拟音频电平变化
    this.simulateAudioLevels();
    
    // 加载自动续费设置
    this.loadAutoRenewalSetting();
    
    // 添加全局选择监听器
    this.initSelectionControl();
  },
  
  beforeUnmount() {
    this.clearAllTimers(); // 使用TimerMixin统一清理
    this.removeSelectionControl(); // 清理选择监听器
  },
  
  methods: {
    // 音频源选择
    selectMicrophoneSource(sourceId) {
      this.selectedMicrophoneSource = sourceId;
      console.log('选择麦克风音频源:', sourceId);
    },
    
    selectSoftwareSource(sourceId) {
      this.selectedSoftwareSource = sourceId;
      console.log('选择软件音频源:', sourceId);
    },
    
    // 检测可用音频源
    detectAudioSources() {
      console.log('检测双音频源...');
      // 模拟检测麦克风设备
      setTimeout(() => {
        this.microphoneSources.forEach(source => {
          source.level = Math.floor(Math.random() * 5) + 1;
        });
        this.softwareAudioSources.forEach(source => {
          source.level = Math.floor(Math.random() * 5) + 1;
        });
      }, 1000);
    },
    
    // 模拟音频电平
    simulateAudioLevels() {
      setInterval(() => {
        if (this.isRecording) {
          // 更新选中的麦克风源电平
          this.microphoneSources.forEach(source => {
            if (source.id === this.selectedMicrophoneSource) {
              source.level = Math.floor(Math.random() * 5) + 1;
            }
          });
          
          // 更新选中的软件源电平
          this.softwareAudioSources.forEach(source => {
            if (source.id === this.selectedSoftwareSource) {
              source.level = Math.floor(Math.random() * 5) + 1;
            }
          });
        }
      }, 200);
    },
    
    // 切换录音状态
    toggleRecording() {
      if (!this.isRecording) {
        // 显示面试确认弹窗
        this.showInterviewConfirmModal = true;
      } else {
        this.stopRecording();
      }
    },
    
    // 开始录音
    startRecording() {
      this.isRecording = true;
      this.startTime = new Date();
      this.startSessionTimer();
      console.log('开始录音...');
      
      // 模拟实时转写
      this.simulateRealTimeTranscription();
    },
    
    // 停止录音
    stopRecording() {
      this.isRecording = false;
      this.clearTimer('sessionTimer'); // 使用TimerMixin
      console.log('停止录音');
      // 打开总结弹窗
      this.showInterviewSummaryModal = true;
    },
    
    // 开始会话计时 - 使用TimerMixin
    startSessionTimer() {
      this.setTimer('sessionTimer', () => {
        if (this.startTime) {
          const now = new Date();
          const diff = now - this.startTime;
          const hours = Math.floor(diff / 3600000);
          const minutes = Math.floor((diff % 3600000) / 60000);
          const seconds = Math.floor((diff % 60000) / 1000);
          
          this.sessionTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
      }, 1000);
    },
    
    // 模拟实时转写（双音频源）
    simulateRealTimeTranscription() {
      // 始终使用双音频模式
      this.simulateDualAudioTranscription();
    },
    

    
    // 双音频转写模式
    simulateDualAudioTranscription() {
      const interviewerQuestions = [
        '请谈谈你对这个岗位的理解？',
        '你有什么优势和不足？',
        '你的职业规划是什么？',
        '为什么选择我们公司？',
        '你有什么问题想问我们的吗？'
      ];
      
      const candidateResponses = [
        '好的，我来简单介绍一下自己的理解...',
        '我认为我最大的优势是学习能力强...',
        '我希望能在技术领域持续深入发展...',
        '我对贵公司的技术栈和企业文化很认同...',
        '请问团队的技术栈和工作方式是怎样的？'
      ];
      
      let questionIndex = 0;
      
      const addInterviewerQuestion = () => {
        if (!this.isRecording || questionIndex >= interviewerQuestions.length) return;
        
        const now = new Date();
        const timestamp = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
        
        // 面试官问题（来自软件音频）
        this.transcript.push({
          type: 'interviewer',
          speaker: '面试官',
          timestamp: timestamp,
          text: interviewerQuestions[questionIndex],
          audioSource: 'software',
          sourceDevice: this.getSelectedSoftwareSource().name
        });
        
        this.questionCount++;
        this.transcriptWordCount += interviewerQuestions[questionIndex].length;
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollTranscriptToBottom();
        });
        
        // 生成建议
        this.generateSuggestions();
        
        // 5秒后模拟候选人回答
        setTimeout(() => {
          if (!this.isRecording) return;
          
          const responseTime = new Date();
          const responseTimestamp = `${responseTime.getHours().toString().padStart(2, '0')}:${responseTime.getMinutes().toString().padStart(2, '0')}:${responseTime.getSeconds().toString().padStart(2, '0')}`;
          
          // 候选人回答（来自麦克风）
          this.transcript.push({
            type: 'candidate',
            speaker: '我',
            timestamp: responseTimestamp,
            text: candidateResponses[questionIndex],
            audioSource: 'microphone',
            sourceDevice: this.getSelectedMicrophoneSource().name
          });
          
          this.transcriptWordCount += candidateResponses[questionIndex].length;
          
          // 滚动到底部
          this.$nextTick(() => {
            this.scrollTranscriptToBottom();
          });
          
          questionIndex++;
          
          // 10秒后添加下一个问题
          setTimeout(addInterviewerQuestion, 10000);
        }, 5000);
      };
      
      // 3秒后开始
      setTimeout(addInterviewerQuestion, 3000);
    },
    
    // 生成建议
    generateSuggestions() {
      this.isGeneratingSuggestion = true;
      
      setTimeout(() => {
        const sampleSuggestions = [
          {
            content: '建议结合具体案例来回答，展现你的实际经验和解决问题的能力。',
            references: ['使用STAR法则组织回答', '量化你的成就和贡献', '展现学习能力和适应性'],
            confidence: Math.floor(Math.random() * 20) + 80,
            generatedAt: new Date().toLocaleTimeString(),
  
          },
          {
            content: '可以从技术能力、团队协作、学习能力等维度来展开回答。',
            references: ['突出与岗位相关的核心技能', '体现团队合作精神', '展现持续学习的态度'],
            confidence: Math.floor(Math.random() * 20) + 80,
            generatedAt: new Date().toLocaleTimeString(),
  
          }
        ];
        
        this.suggestions = sampleSuggestions;
        this.suggestionCount += sampleSuggestions.length;
        this.isGeneratingSuggestion = false;
      }, 2000);
    },
    
    // 重新生成建议
    regenerateSuggestions() {
      this.generateSuggestions();
    },
    
    // 清空转写记录
    clearTranscript() {
      this.transcript = [];
      this.suggestions = [];
      this.transcriptWordCount = 0;
      this.questionCount = 0;
      this.suggestionCount = 0;
    },
    
    
    

    

    

    
    // 滚动转写到底部
    scrollTranscriptToBottom() {
      const element = this.$refs.transcriptContent;
      if (element) {
        element.scrollTop = element.scrollHeight;
      }
    },
    

    // 导出报告
    exportReport() {
      console.log('导出报告');
      this.showInfoToast('报告导出中...');
    },

    // 跳转到总结页面
    goToSummary() {
      // 构建当前面试数据
      const currentInterviewData = {
        id: `interview_${Date.now()}`,
        title: this.interviewPosition ? `${this.interviewPosition}面试总结` : '当前面试总结',
        company: '未知公司',
        position: this.interviewPosition || '未填写职位',
        date: new Date().toISOString().split('T')[0],
        startTime: this.startTime ? this.startTime.toTimeString().split(' ')[0] : '00:00:00',
        endTime: new Date().toTimeString().split(' ')[0],
        duration: this.sessionTime,
        status: 'completed',
        score: Math.floor(Math.random() * 20) + 80, // 模拟评分 80-100
        summary: {
          transcriptWordCount: this.transcriptWordCount,
          questionCount: this.questionCount,
          suggestionCount: this.suggestionCount,
          averageResponseTime: 2.1,
          confidenceScore: 0.85
        },
        highlights: [
          '表达清晰流畅',
          '技术理解深入',
          '回答逻辑性强'
        ],
        improvements: [
          '可以增加具体案例',
          '技术细节可以更深入'
        ],
        tags: ['技术面试', '实时辅助']
      };

      // 关闭弹窗
      this.closeInterviewSummaryModal();

      // 跳转到总结页面，并传递当前面试数据
      this.$router.push({
        name: 'Summary',
        query: {
          current: encodeURIComponent(JSON.stringify(currentInterviewData))
        }
      });
    },
    
    // 同意并开始
    agreeAndStart() {
      this.showEthicsModal = false;
      console.log('用户同意伦理声明');
    },
    
    // 获取选中的音频源
    getSelectedMicrophoneSource() {
      return this.microphoneSources.find(source => source.id === this.selectedMicrophoneSource) || this.microphoneSources[0];
    },
    
    getSelectedSoftwareSource() {
      return this.softwareAudioSources.find(source => source.id === this.selectedSoftwareSource) || this.softwareAudioSources[0];
    },
    
    // 音频源模态框控制
    closeMicrophoneSourceModal() {
      this.showMicrophoneSourceModal = false;
    },
    
    closeSoftwareSourceModal() {
      this.showSoftwareSourceModal = false;
    },
    
    // 确认音频源选择
    confirmMicrophoneSource() {
      this.showMicrophoneSourceModal = false;
      console.log('确认麦克风音频源:', this.selectedMicrophoneSource);
      this.showToast('success', '音频源切换成功', `麦克风已切换到: ${this.getSelectedMicrophoneSource().name}`);
    },
    
    confirmSoftwareSource() {
      this.showSoftwareSourceModal = false;
      console.log('确认软件音频源:', this.selectedSoftwareSource);
      this.showToast('success', '音频源切换成功', `软件音频已切换到: ${this.getSelectedSoftwareSource().name}`);
    },
    
    // 保存转写记录
    saveTranscriptRecord() {
      if (this.transcript.length === 0) {
        this.showWarningToast('暂无转写内容可记录');
        return;
      }
      
      const record = {
        id: `record_${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        content: this.transcript.map(item => `${item.speaker}: ${item.text}`).join('\n'),
        type: 'transcript'
      };
      
      console.log('保存转写记录:', record);
      this.showSuccessToast('转写记录已保存');
    },
    
    // 保存建议记录
    saveSuggestionRecord() {
      if (this.suggestions.length === 0) {
        this.showWarningToast('暂无建议内容可记录');
        return;
      }
      
      const record = {
        id: `record_${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        content: this.suggestions.map((item, index) => `建议${index + 1}: ${item.content}`).join('\n\n'),
        type: 'suggestion'
      };
      
      console.log('保存建议记录:', record);
      this.showSuccessToast('建议记录已保存');
    },
    
    // 面试确认弹窗相关方法
    closeInterviewConfirmModal() {
      this.showInterviewConfirmModal = false;
    },
    
    confirmStartInterview() {
      // 关闭弹窗
      this.showInterviewConfirmModal = false;
      // 开始录音
      this.startRecording();
    },
    
    // 保存自动续费设置
    saveAutoRenewalSetting() {
      this.saveToStorage('autoRenewalEnabled', this.autoRenewalEnabled);
      console.log('自动续费设置已保存:', this.autoRenewalEnabled);
    },
    
    // 加载自动续费设置
    loadAutoRenewalSetting() {
      const saved = this.loadFromStorage('autoRenewalEnabled');
      if (saved !== null) {
        this.autoRenewalEnabled = saved;
      }
    },
    
    // 确认离开面试页面
    confirmInterviewExit() {
      this.showInterviewExitConfirm = false;
      if (this.pendingRoute) {
        // 用户确认离开，停止当前面试
        if (this.isRecording) {
          this.stopRecording();
        }
        // 执行路由跳转
        this.pendingRoute.next();
        // 清理待跳转路由
        this.pendingRoute = null;
      }
    },
    
    // 取消离开面试页面
    cancelInterviewExit() {
      this.showInterviewExitConfirm = false;
      if (this.pendingRoute) {
        // 用户取消离开，阻止路由跳转
        this.pendingRoute.next(false);
        // 清理待跳转路由
        this.pendingRoute = null;
      }
    },

    // 关闭面试总结弹窗
    closeInterviewSummaryModal() {
      this.showInterviewSummaryModal = false;
    },
    
    // Toast 弹窗方法
    showToast(type = 'success', title = '', message = '') {
      const iconMap = {
        success: '/icons/chenggong.png',
        error: '/icons/cuowu.png',
        warning: '/icons/zhuyi.png',
        info: '/icons/xinxi.png'
      };
      
      this.toastConfig = {
        show: true,
        type: type,
        title: title,
        message: message,
        icon: iconMap[type] || iconMap.success
      };
      
      // 3秒后自动隐藏
      setTimeout(() => {
        this.toastConfig.show = false;
      }, 3000);
    },
    
    // 文本选择控制方法
    initSelectionControl() {
      // 添加全局选择变化监听器
      document.addEventListener('selectionchange', this.handleSelectionChange);
      // 添加全局鼠标松开监听器
      document.addEventListener('mouseup', this.handleMouseUp);
    },
    
    removeSelectionControl() {
      // 清理监听器
      document.removeEventListener('selectionchange', this.handleSelectionChange);
      document.removeEventListener('mouseup', this.handleMouseUp);
    },
    
    handleSelectionStart() {
      // 选择开始时不需要持久记录，在 selectionchange 中动态判断
    },
    
    handleSelectionChange() {
      const selection = window.getSelection();
      
      if (selection.rangeCount === 0) {
        return;
      }
      
      const range = selection.getRangeAt(0);
      const startContainer = range.startContainer;
      const endContainer = range.endContainer;
      
      // 获取选择开始和结束的转写/建议条目
      const startItem = this.findSelectionContainer(startContainer);
      const endItem = this.findSelectionContainer(endContainer);
      
      // 如果选择跨越了不同的条目，清除选择
      if (startItem && endItem && startItem !== endItem) {
        selection.removeAllRanges();
      }
    },
    
    handleMouseUp() {
      // 鼠标松开时无需额外处理
    },
    
    findSelectionContainer(node) {
      // 向上查找直到找到转写条目或建议条目
      let current = node;
      while (current && current !== document.body) {
        if (current.classList && 
            (current.classList.contains('transcript-item') || 
             current.classList.contains('suggestion-item'))) {
          return current;
        }
        current = current.parentNode;
      }
      return null;
    },
  },
  
  watch: {
    // 监听布局模式变化 - 使用StorageMixin
    layoutMode(newMode) {
      console.log('布局模式切换为:', newMode);
      this.saveToStorage('interviewLayoutMode', newMode);
    }
  }
}