// 充值页面逻辑文件
// 处理套餐选择、支付流程、数据持久化等功能

import packagesData from '../../assets/json/packages.json';

// Vue组件配置
export const MoneyComponent = {
  name: 'Money',
  data() {
    return {
      // 当前会员状态
      currentPlan: {},
      // 选中的套餐和支付方式
      selectedPackage: null,
      selectedPayment: null,
      isProcessing: false,
      showSuccessDialog: false,
      showErrorDialog: false,
      errorMessage: '',
      // 数据加载状态
      loading: true,
      error: null,
      // 套餐列表
      packages: [],
      // 支付方式
      paymentMethods: [],
      // 充值记录
      rechargeHistory: []
    }
  },
  mounted() {
    // 加载套餐数据
    this.loadPackagesData();
    
    // 初始化充值页面
    if (window.moneyPageInit) {
      window.moneyPageInit();
    }
  },
  methods: {
    // 加载套餐数据
    loadPackagesData() {
      try {
        // 检查数据格式
        if (packagesData.code === 200 && packagesData.data) {
          const { data } = packagesData;
          
          // 加载数据
          this.currentPlan = data.currentPlan || {};
          this.packages = data.packages || [];
          this.paymentMethods = data.paymentMethods || [];
          this.rechargeHistory = data.rechargeHistory || [];
          
          this.loading = false;
          console.log('套餐数据加载成功:', packagesData);
        } else {
          throw new Error(`数据格式错误: ${packagesData.message || '未知错误'}`);
        }
      } catch (error) {
        console.error('加载套餐数据失败:', error);
        this.error = error.message;
        this.loading = false;
        
        // 显示错误提示
        this.showErrorToast('数据加载失败，请刷新页面重试');
      }
    },

    // 注意：showErrorMessage方法已移到ToastMixin中，现在使用showErrorToast

    selectPackage(packageId) {
      this.selectedPackage = packageId;
      // 默认选择微信支付
      this.selectedPayment = 'wechat';
    },
    selectPayment(paymentId) {
      this.selectedPayment = paymentId;
    },
    getSelectedPackage() {
      return this.packages.find(pkg => pkg.id === this.selectedPackage) || {};
    },
    getSelectedPayment() {
      return this.paymentMethods.find(method => method.id === this.selectedPayment) || {};
    },
    async processPay() {
      this.isProcessing = true;
      
      try {
        // 模拟支付处理
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 调用全局支付处理函数
        if (window.processPayment) {
          const result = await window.processPayment({
            packageId: this.selectedPackage,
            paymentMethod: this.selectedPayment,
            amount: this.getSelectedPackage().price
          });
          
          if (result.success) {
            this.showSuccessDialog = true;
            this.updateCurrentPlan();
            this.addToHistory();
          }
        } else {
          // 模拟成功
          this.showSuccessDialog = true;
          this.updateCurrentPlan();
          this.addToHistory();
        }
      } catch (error) {
        console.error('支付失败:', error);
        // 显示错误弹窗
        let errorMessage = error.message || '支付失败，请重试';
        
        // 如果是企业认证错误，提供更详细的指导
        if (error.message && error.message.includes('企业身份验证')) {
          errorMessage = '企业版需要企业身份验证，请先完成企业认证后再购买企业版套餐';
        }
        
        this.errorMessage = errorMessage;
        this.showErrorDialog = true;
      } finally {
        this.isProcessing = false;
      }
    },
    updateCurrentPlan() {
      const selectedPkg = this.getSelectedPackage();
      this.currentPlan = {
        name: selectedPkg.name,
        remaining: selectedPkg.usage.includes('无限') ? '无限' : selectedPkg.usage.match(/\d+/)[0],
        total: selectedPkg.usage.includes('无限') ? '无限' : selectedPkg.usage.match(/\d+/)[0],
        expireDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
      };
    },
    addToHistory() {
      const selectedPkg = this.getSelectedPackage();
      this.rechargeHistory.unshift({
        id: Date.now(),
        date: new Date().toLocaleDateString(),
        packageName: selectedPkg.name,
        amount: selectedPkg.price,
        status: 'success'
      });
    },
    closeSuccessDialog() {
      this.showSuccessDialog = false;
      this.selectedPackage = null;
      this.selectedPayment = null;
    },
    closeErrorDialog() {
      this.showErrorDialog = false;
      this.errorMessage = '';
    },
    getStatusText(status) {
      const statusMap = {
        'success': '成功',
        'pending': '处理中',
        'failed': '失败'
      };
      return statusMap[status] || '未知';
    }
  }
};

// 全局充值页面数据
window.moneyPageData = {
  // 当前用户信息
  currentUser: {
    id: null,
    email: '',
    phone: ''
  },
  
  // 当前会员状态
  currentPlan: {
    id: 'free',
    name: '免费版',
    remaining: 3,
    total: 5,
    expireDate: '永久有效',
    type: 'free'
  },
  
  // 订单状态
  currentOrder: null,
  
  // 支付配置
  paymentConfig: {
    wechat: {
      appId: 'demo_app_id',
      enabled: true
    },
    alipay: {
      appId: 'demo_alipay_id', 
      enabled: true
    },
    card: {
      enabled: true
    }
  },
  
  // 套餐配置（可以从后端获取）
  packageConfig: {
    basic: {
      id: 'basic',
      name: '基础版',
      price: 29,
      originalPrice: 39,
      usage: '50次/月使用次数',
      duration: 30, // 天数
      features: [
        'AI简历优化',
        '面试题库访问',
        '基础数据分析',
        '邮件支持'
      ],
      limits: {
        dailyUsage: 2,
        monthlyUsage: 50,
        features: ['resume_optimize', 'interview_questions', 'basic_analysis']
      }
    },
    premium: {
      id: 'premium',
      name: '豪华版',
      price: 59,
      originalPrice: 79,
      usage: '150次/月使用次数',
      duration: 30,
      popular: true,
      features: [
        '包含基础版所有功能',
        'AI模拟面试',
        '个性化简历模板',
        '优先客服支持',
        '职业发展建议'
      ],
      limits: {
        dailyUsage: 5,
        monthlyUsage: 150,
        features: ['all_basic', 'ai_interview', 'premium_templates', 'priority_support']
      }
    },
    ultimate: {
      id: 'ultimate',
      name: '终极版',
      price: 99,
      originalPrice: 129,
      usage: '无限次数使用',
      duration: 30,
      features: [
        '包含豪华版所有功能',
        '专属面试官指导',
        '行业专家咨询',
        '定制化职业规划',
        '1对1专属服务'
      ],
      limits: {
        dailyUsage: -1, // -1表示无限制
        monthlyUsage: -1,
        features: ['all_premium', 'expert_guidance', 'custom_planning', 'one_on_one']
      }
    },
    student: {
      id: 'student',
      name: '学生版',
      price: 19,
      originalPrice: 29,
      usage: '30次/月使用次数',
      duration: 30,
      features: [
        'AI简历优化',
        '校招面试题库',
        '实习机会推荐',
        '学生专属模板'
      ],
      limits: {
        dailyUsage: 1,
        monthlyUsage: 30,
        features: ['resume_optimize', 'campus_interview', 'internship_recommend']
      },
      requirements: {
        needStudentVerification: true
      }
    },
    enterprise: {
      id: 'enterprise',
      name: '企业版',
      price: 299,
      originalPrice: 399,
      usage: '无限次数 + 团队管理',
      duration: 30,
      type: 'enterprise',
      features: [
        '包含终极版所有功能',
        '团队账户管理',
        '批量简历处理',
        '企业定制服务',
        '专属客户经理',
        '数据报表分析'
      ],
      limits: {
        dailyUsage: -1,
        monthlyUsage: -1,
        teamMembers: 20,
        features: ['all_ultimate', 'team_management', 'batch_processing', 'enterprise_service']
      },
      requirements: {
        needEnterpriseVerification: true,
        minUsers: 5
      }
    }
  }
};

// 页面初始化
window.moneyPageInit = function() {
  console.log('充值页面初始化');
  
  // 加载用户数据
  loadUserData();
  
  // 加载会员状态
  loadMembershipStatus();
  
  // 初始化支付配置
  initPaymentMethods();
  
  // 绑定事件监听器
  bindEventListeners();
  
  console.log('充值页面初始化完成');
};

// 加载用户数据
window.loadUserData = function() {
  try {
    const userData = localStorage.getItem('userInfo');
    if (userData) {
      const user = JSON.parse(userData);
      window.moneyPageData.currentUser = {
        id: user.id || null,
        email: user.email || '',
        phone: user.phone || ''
      };
      console.log('用户数据加载完成');
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
  }
};

// 加载会员状态
window.loadMembershipStatus = function() {
  try {
    const membershipData = localStorage.getItem('membershipStatus');
    if (membershipData) {
      const membership = JSON.parse(membershipData);
      window.moneyPageData.currentPlan = {
        ...window.moneyPageData.currentPlan,
        ...membership
      };
      console.log('会员状态加载完成:', window.moneyPageData.currentPlan);
    }
  } catch (error) {
    console.error('加载会员状态失败:', error);
  }
};

// 保存会员状态
window.saveMembershipStatus = function(membershipData) {
  try {
    localStorage.setItem('membershipStatus', JSON.stringify(membershipData));
    window.moneyPageData.currentPlan = { ...window.moneyPageData.currentPlan, ...membershipData };
    console.log('会员状态保存成功');
  } catch (error) {
    console.error('保存会员状态失败:', error);
  }
};

// 初始化支付方式
window.initPaymentMethods = function() {
  // 检查各种支付方式的可用性
  const userAgent = navigator.userAgent;
  
  // 微信环境检测
  if (userAgent.toLowerCase().includes('micromessenger')) {
    window.moneyPageData.paymentConfig.wechat.inWechat = true;
  }
  
  // 支付宝环境检测
  if (userAgent.toLowerCase().includes('alipayclient')) {
    window.moneyPageData.paymentConfig.alipay.inAlipay = true;
  }
  
  console.log('支付方式初始化完成:', window.moneyPageData.paymentConfig);
};

// 绑定事件监听器
window.bindEventListeners = function() {
  // 页面卸载时的清理工作
  window.addEventListener('beforeunload', function() {
    // 保存当前数据
    saveCurrentData();
  });
  
  // 监听网络状态变化
  window.addEventListener('online', function() {
    console.log('网络连接恢复');
  });
  
  window.addEventListener('offline', function() {
    console.log('网络连接断开');
  });
};

// 保存当前数据
window.saveCurrentData = function() {
  try {
    // 保存会员状态
    if (window.moneyPageData.currentPlan) {
      saveMembershipStatus(window.moneyPageData.currentPlan);
    }
    
    // 保存其他数据...
    console.log('数据保存完成');
  } catch (error) {
    console.error('保存数据失败:', error);
  }
};

// 获取套餐信息
window.getPackageInfo = function(packageId) {
  return window.moneyPageData.packageConfig[packageId] || null;
};

// 计算套餐价格（含折扣）
window.calculatePackagePrice = function(packageId, discountCode = null) {
  const pkg = getPackageInfo(packageId);
  if (!pkg) return null;
  
  let finalPrice = pkg.price;
  let discount = 0;
  
  // 应用折扣码
  if (discountCode) {
    discount = applyDiscountCode(discountCode, pkg);
    finalPrice = Math.max(0, pkg.price - discount);
  }
  
  return {
    originalPrice: pkg.originalPrice || pkg.price,
    currentPrice: pkg.price,
    finalPrice: finalPrice,
    discount: discount,
    discountCode: discountCode
  };
};

// 应用折扣码
window.applyDiscountCode = function(discountCode, packageInfo) {
  // 折扣码配置
  const discountCodes = {
    'FIRST10': { type: 'percentage', value: 10, description: '首次购买9折优惠' },
    'STUDENT20': { type: 'percentage', value: 20, description: '学生专享8折优惠' },
    'WELCOME': { type: 'fixed', value: 5, description: '新用户立减5元' }
  };
  
  const discount = discountCodes[discountCode.toUpperCase()];
  if (!discount) return 0;
  
  if (discount.type === 'percentage') {
    return Math.floor(packageInfo.price * discount.value / 100);
  } else if (discount.type === 'fixed') {
    return discount.value;
  }
  
  return 0;
};

// 验证套餐购买资格
window.validatePackagePurchase = function(packageId) {
  const pkg = getPackageInfo(packageId);
  if (!pkg) {
    return { valid: false, message: '套餐不存在' };
  }
  
  // 检查特殊要求
  if (pkg.requirements) {
    // 学生版验证
    if (pkg.requirements.needStudentVerification) {
      const studentStatus = localStorage.getItem('studentVerified');
      if (!studentStatus || studentStatus !== 'true') {
        return { 
          valid: false, 
          message: '学生版需要学生身份验证，请先完成学生认证',
          action: 'student_verify'
        };
      }
    }
    
    // 企业版验证
    if (pkg.requirements.needEnterpriseVerification) {
      const enterpriseStatus = localStorage.getItem('enterpriseVerified');
      if (!enterpriseStatus || enterpriseStatus !== 'true') {
        return { 
          valid: false, 
          message: '企业版需要企业身份验证，请先完成企业认证',
          action: 'enterprise_verify'
        };
      }
    }
  }
  
  return { valid: true };
};

// 创建订单
window.createOrder = function(packageId, paymentMethod, userInfo = {}) {
  const validation = validatePackagePurchase(packageId);
  if (!validation.valid) {
    return Promise.reject(new Error(validation.message));
  }
  
  const pkg = getPackageInfo(packageId);
  const priceInfo = calculatePackagePrice(packageId);
  
  const order = {
    id: generateOrderId(),
    packageId: packageId,
    packageName: pkg.name,
    amount: priceInfo.finalPrice,
    originalAmount: priceInfo.currentPrice,
    discount: priceInfo.discount,
    paymentMethod: paymentMethod,
    status: 'pending',
    createTime: new Date().toISOString(),
    userInfo: {
      ...window.moneyPageData.currentUser,
      ...userInfo
    },
    packageInfo: pkg
  };
  
  // 保存订单到本地存储
  saveOrderToStorage(order);
  window.moneyPageData.currentOrder = order;
  
  console.log('订单创建成功:', order);
  return Promise.resolve(order);
};

// 生成订单ID
window.generateOrderId = function() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000);
  return `ORDER_${timestamp}_${random}`;
};

// 保存订单到本地存储
window.saveOrderToStorage = function(order) {
  try {
    let orders = JSON.parse(localStorage.getItem('rechargeOrders') || '[]');
    orders.unshift(order);
    
    // 只保留最近50个订单
    if (orders.length > 50) {
      orders = orders.slice(0, 50);
    }
    
    localStorage.setItem('rechargeOrders', JSON.stringify(orders));
  } catch (error) {
    console.error('保存订单失败:', error);
  }
};

// 获取充值记录
window.getRechargeHistory = function() {
  try {
    const orders = JSON.parse(localStorage.getItem('rechargeOrders') || '[]');
    return orders.map(order => ({
      id: order.id,
      date: new Date(order.createTime).toLocaleDateString(),
      packageName: order.packageName,
      amount: order.amount,
      status: order.status
    }));
  } catch (error) {
    console.error('获取充值记录失败:', error);
    return [];
  }
};

// 处理支付
window.processPayment = function(paymentData) {
  return new Promise((resolve, reject) => {
    console.log('开始处理支付:', paymentData);
    
    const { packageId, paymentMethod, amount } = paymentData;
    
    // 创建订单
    createOrder(packageId, paymentMethod)
      .then(order => {
        // 根据支付方式处理支付
        switch (paymentMethod) {
          case 'wechat':
            return processWechatPay(order);
          case 'alipay':
            return processAlipayPay(order);
          case 'card':
            return processCardPay(order);
          default:
            throw new Error('不支持的支付方式');
        }
      })
      .then(paymentResult => {
        if (paymentResult.success) {
          // 支付成功，更新会员状态
          const pkg = getPackageInfo(packageId);
          updateMembershipStatus(pkg);
          
          // 更新订单状态
          updateOrderStatus(window.moneyPageData.currentOrder.id, 'success');
          
          resolve({
            success: true,
            orderId: window.moneyPageData.currentOrder.id,
            message: '支付成功'
          });
        } else {
          // 支付失败
          updateOrderStatus(window.moneyPageData.currentOrder.id, 'failed');
          reject(new Error(paymentResult.message || '支付失败'));
        }
      })
      .catch(error => {
        console.error('支付处理失败:', error);
        if (window.moneyPageData.currentOrder) {
          updateOrderStatus(window.moneyPageData.currentOrder.id, 'failed');
        }
        reject(error);
      });
  });
};

// 微信支付处理
window.processWechatPay = function(order) {
  return new Promise((resolve) => {
    console.log('处理微信支付:', order);
    
    // 模拟微信支付流程
    setTimeout(() => {
      // 在实际应用中，这里会调用微信支付API
      resolve({ success: true, message: '微信支付成功' });
    }, 2000);
  });
};

// 支付宝支付处理
window.processAlipayPay = function(order) {
  return new Promise((resolve) => {
    console.log('处理支付宝支付:', order);
    
    // 模拟支付宝支付流程
    setTimeout(() => {
      // 在实际应用中，这里会调用支付宝支付API
      resolve({ success: true, message: '支付宝支付成功' });
    }, 2000);
  });
};

// 银行卡支付处理
window.processCardPay = function(order) {
  return new Promise((resolve) => {
    console.log('处理银行卡支付:', order);
    
    // 模拟银行卡支付流程
    setTimeout(() => {
      // 在实际应用中，这里会调用银行卡支付API
      resolve({ success: true, message: '银行卡支付成功' });
    }, 2000);
  });
};

// 更新订单状态
window.updateOrderStatus = function(orderId, status) {
  try {
    let orders = JSON.parse(localStorage.getItem('rechargeOrders') || '[]');
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex !== -1) {
      orders[orderIndex].status = status;
      orders[orderIndex].updateTime = new Date().toISOString();
      localStorage.setItem('rechargeOrders', JSON.stringify(orders));
      console.log('订单状态更新成功:', orderId, status);
    }
  } catch (error) {
    console.error('更新订单状态失败:', error);
  }
};

// 更新会员状态
window.updateMembershipStatus = function(packageInfo) {
  const currentDate = new Date();
  const expireDate = new Date(currentDate.getTime() + packageInfo.duration * 24 * 60 * 60 * 1000);
  
  const newMembership = {
    id: packageInfo.id,
    name: packageInfo.name,
    remaining: packageInfo.limits.monthlyUsage === -1 ? '无限' : packageInfo.limits.monthlyUsage,
    total: packageInfo.limits.monthlyUsage === -1 ? '无限' : packageInfo.limits.monthlyUsage,
    expireDate: expireDate.toLocaleDateString(),
    type: packageInfo.type || 'premium',
    features: packageInfo.features,
    limits: packageInfo.limits,
    activateDate: currentDate.toISOString()
  };
  
  saveMembershipStatus(newMembership);
  console.log('会员状态更新成功:', newMembership);
};

// 检查会员状态
window.checkMembershipStatus = function() {
  const membership = window.moneyPageData.currentPlan;
  if (!membership || membership.type === 'free') {
    return { active: false, message: '当前为免费用户' };
  }
  
  const expireDate = new Date(membership.expireDate);
  const currentDate = new Date();
  
  if (expireDate < currentDate) {
    return { active: false, message: '会员已过期' };
  }
  
  return { active: true, membership: membership };
};

// 获取可用功能列表
window.getAvailableFeatures = function() {
  const membership = window.moneyPageData.currentPlan;
  if (!membership || !membership.features) {
    return ['basic_resume']; // 免费用户的基础功能
  }
  
  return membership.limits.features || [];
};

// 检查功能使用权限
window.checkFeaturePermission = function(featureName) {
  const availableFeatures = getAvailableFeatures();
  return availableFeatures.includes(featureName) || availableFeatures.includes('all_premium') || availableFeatures.includes('all_ultimate');
};

// 记录功能使用
window.recordFeatureUsage = function(featureName) {
  try {
    const usageKey = `feature_usage_${new Date().toDateString()}`;
    let dailyUsage = JSON.parse(localStorage.getItem(usageKey) || '{}');
    
    dailyUsage[featureName] = (dailyUsage[featureName] || 0) + 1;
    localStorage.setItem(usageKey, JSON.stringify(dailyUsage));
    
    console.log('功能使用记录:', featureName, dailyUsage[featureName]);
  } catch (error) {
    console.error('记录功能使用失败:', error);
  }
};

// 获取今日使用次数
window.getTodayUsage = function(featureName = null) {
  try {
    const usageKey = `feature_usage_${new Date().toDateString()}`;
    const dailyUsage = JSON.parse(localStorage.getItem(usageKey) || '{}');
    
    if (featureName) {
      return dailyUsage[featureName] || 0;
    }
    
    return dailyUsage;
  } catch (error) {
    console.error('获取使用次数失败:', error);
    return featureName ? 0 : {};
  }
};

// 页面清理
window.moneyPageCleanup = function() {
  console.log('充值页面清理');
  
  // 保存当前数据
  saveCurrentData();
  
  // 清理定时器等资源
  // ...
  
  console.log('充值页面清理完成');
};

// 工具函数
window.formatPrice = function(price) {
  return `¥${price.toFixed(2)}`;
};

window.formatDate = function(date) {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  return date.toLocaleDateString('zh-CN');
};

window.formatDateTime = function(date) {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  return date.toLocaleString('zh-CN');
};

// 导出给外部使用的API
window.MoneyPageAPI = {
  init: window.moneyPageInit,
  cleanup: window.moneyPageCleanup,
  getPackageInfo: window.getPackageInfo,
  processPayment: window.processPayment,
  checkMembershipStatus: window.checkMembershipStatus,
  getAvailableFeatures: window.getAvailableFeatures,
  checkFeaturePermission: window.checkFeaturePermission,
  recordFeatureUsage: window.recordFeatureUsage,
  getTodayUsage: window.getTodayUsage,
  getRechargeHistory: window.getRechargeHistory
};

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', window.moneyPageInit);
} else {
  window.moneyPageInit();
}2212402100109