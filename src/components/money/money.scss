// 充值页面样式

// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$gray: #a0a5a8;
$black: #181818;
$purple: #4B70E2;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-muted: #94a3b8;
$success-color: #10b981;
$warning-color: #f59e0b;
$error-color: #ef4444;
$transition: 0.3s;
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-premium: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
$gradient-enterprise: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-error: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
$gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

// 主容器
.money-container {
  width: 100%;
  height: 100%;
  background-color: $neu-1;
  overflow-y: auto;
  padding: 20px;
  cursor: default;
}

.money-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  cursor: default;
}

// 页面标题区域
.money-header {
  text-align: center;
  margin-bottom: 40px;
  
  .money-title {
    font-size: 42px;
    font-weight: 700;
    color: $black;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba($neu-2, 0.3);
    
    background: $gradient-primary;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    cursor: default;
    user-select: none;
  }
  
  .money-subtitle {
    font-size: 18px;
    color: $gray;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
    cursor: default;
    user-select: none;
  }
}

// 当前状态卡片
.current-status {
  margin-bottom: 40px;
  
  .status-card {
    background-color: $neu-1;
    border-radius: 20px;
    padding: 30px;
    box-shadow:
      12px 12px 20px $neu-2,
      -12px -12px 20px $white;
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 30px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
      padding: 20px;
    }
    
    .status-info {
      text-align: center;
      
      .status-label {
        display: block;
        font-size: 14px;
        color: $text-secondary;
        margin-bottom: 8px;
        font-weight: 500;
        cursor: default;
        user-select: none;
      }
      
      .status-value {
        display: block;
        font-size: 24px;
        font-weight: 700;
        color: $purple;
        cursor: default;
        user-select: none;
      }
    }
  }
}

// 区域标题
.section-title {
  font-size: 28px;
  font-weight: 600;
  color: $black;
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  cursor: default;
  user-select: none;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: $gradient-primary;
    border-radius: 2px;
  }
}

// 套餐选择区域
.packages-section {
  margin-bottom: 40px;
}

// 套餐网格
.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

// 套餐卡片
.package-card {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 30px 25px;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  transition: all $transition ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-primary;
    opacity: 0;
    transition: opacity $transition ease;
  }
  
  &:hover {
    transform: translateY(-5px);
    box-shadow:
      12px 12px 24px $neu-2,
      -12px -12px 24px $white;
    
    &::before {
      opacity: 1;
    }
  }
  
  &.selected {
    box-shadow:
      inset 4px 4px 8px $neu-2,
      inset -4px -4px 8px $white;
    transform: translateY(2px);
    
    &::before {
      opacity: 1;
    }
  }
  
  &.popular {
    &::before {
      background: $gradient-premium;
      opacity: 1;
    }
  }
  
  &.enterprise {
    &::before {
      background: $gradient-enterprise;
      opacity: 1;
    }
  }
}

// 套餐头部
.package-header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
  
  .package-name {
    font-size: 24px;
    font-weight: 700;
    color: $black;
    margin-bottom: 10px;
    cursor: default;
    user-select: none;
  }
  
  .popular-badge,
  .enterprise-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    box-shadow:
      4px 4px 8px rgba($neu-2, 0.6),
      -2px -2px 4px rgba($white, 0.6);
    cursor: default;
    user-select: none;
  }
  
  .popular-badge {
    background: $gradient-premium;
  }
  
  .enterprise-badge {
    background: $gradient-enterprise;
  }
}

// 套餐价格
.package-price {
  text-align: center;
  margin-bottom: 25px;
  
  .currency {
    font-size: 18px;
    color: $text-secondary;
    vertical-align: top;
    cursor: default;
    user-select: none;
  }
  
  .amount {
    font-size: 48px;
    font-weight: 700;
    color: $purple;
    margin: 0 5px;
    cursor: default;
    user-select: none;
  }
  
  .period {
    font-size: 16px;
    color: $text-secondary;
    cursor: default;
    user-select: none;
  }
}

// 套餐功能列表
.package-features {
  margin-bottom: 25px;
  
  .feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    
    .feature-icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      
      &.check-icon::before {
        content: '';
        display: block;
        width: 20px;
        height: 20px;
        background-color: $success-color;
        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='3'%3E%3Cpolyline points='20,6 9,17 4,12'/%3E%3C/svg%3E");
        mask-size: contain;
        mask-repeat: no-repeat;
        mask-position: center;
      }
    }
    
    .feature-text {
      font-size: 14px;
      color: $text-primary;
      line-height: 1.4;
      cursor: default;
      user-select: none;
    }
  }
}

// 套餐按钮
.package-btn {
  width: 100%;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  background-color: $neu-1;
  color: $purple;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all $transition ease;
  box-shadow:
    4px 4px 8px $neu-2,
    -4px -4px 8px $white;
  outline: none;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      6px 6px 12px $neu-2,
      -6px -6px 12px $white;
  }
  
  &:active,
  &.selected {
    transform: translateY(1px);
    box-shadow:
      inset 4px 4px 8px $neu-2,
      inset -4px -4px 8px $white;
    background: $gradient-primary;
    color: white;
  }
}

// 支付方式和订单确认 - 网格布局容器
.payment-order-section {
  display: grid;
  grid-template-columns: 366px 1fr;
  gap: 30px;
  margin-bottom: 60px;
  
  @media (max-width: 1024px) {
    grid-template-columns: 320px 1fr;
  }
  
  @media (max-width: 900px) {
    grid-template-columns: 280px 1fr;
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
  }
}

// 支付方式区域
.payment-section {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 30px 25px;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  transition: all $transition ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-primary;
    opacity: 1;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      12px 12px 24px $neu-2,
      -12px -12px 24px $white;
  }
}

// 支付方式包装容器
.payment-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: $black;
    margin-bottom: 25px;
    text-align: center;
    cursor: default;
    user-select: none;
    flex-shrink: 0;
  }
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
  justify-content: flex-start;
}

// 支付方式选项
.payment-method {
  background-color: $neu-1;
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  transition: all $transition ease;
  
  &:hover {
    border-color: rgba($purple, 0.3);
    background-color: rgba($purple, 0.02);
    
    .payment-icon img {
      opacity: 1;
    }
  }
  
  &.selected {
    border-color: $purple;
    background-color: rgba($purple, 0.05);
    
    .payment-icon img {
      opacity: 1;
    }
    
    .payment-radio {
      border-color: $purple;
      
      .radio-dot {
        background: $purple;
        transform: scale(1);
      }
    }
  }
  
  .payment-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($purple, 0.1);
    border: 1px solid rgba($purple, 0.2);
    
    img {
      width: 28px;
      height: 28px;
      opacity: 0.8;
      transition: opacity 0.2s ease;
    }
  }
  
  .payment-info {
    flex: 1;
    
    .payment-name {
      font-size: 18px;
      font-weight: 600;
      color: $black;
      margin-bottom: 4px;
      cursor: default;
      user-select: none;
    }
    
    .payment-desc {
      font-size: 14px;
      color: $text-secondary;
      cursor: default;
      user-select: none;
    }
  }
  
  .payment-radio {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all $transition ease;
    background-color: $white;
    
    .radio-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: transparent;
      transform: scale(0);
      transition: all $transition ease;
    }
  }
}

// 订单确认区域
.order-section {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 30px 25px;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  transition: all $transition ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-primary;
    opacity: 1;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      12px 12px 24px $neu-2,
      -12px -12px 24px $white;
  }
}

.order-summary {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .order-title {
    font-size: 24px;
    font-weight: 700;
    color: $black;
    text-align: center;
    margin-bottom: 25px;
    cursor: default;
    user-select: none;
    flex-shrink: 0;
  }
  
  .order-details {
    flex: 1;
    margin-bottom: 25px;
    
    .order-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba($text-muted, 0.2);
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-label {
        font-size: 16px;
        color: $text-secondary;
        cursor: default;
        user-select: none;
      }
      
      .item-value {
        font-size: 16px;
        font-weight: 600;
        color: $black;
        cursor: default;
        user-select: none;
      }
    }
    
    .order-total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0 10px;
      margin-top: 15px;
      border-top: 2px solid rgba($purple, 0.2);
      
      .total-label {
        font-size: 20px;
        font-weight: 700;
        color: $black;
        cursor: default;
        user-select: none;
      }
      
      .total-amount {
        font-size: 24px;
        font-weight: 700;
        color: $purple;
        cursor: default;
        user-select: none;
      }
    }
  }
}

// 支付按钮
.pay-btn {
  width: 100%;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  background: $gradient-primary;
  color: white;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all $transition ease;
  flex-shrink: 0;
  margin-top: auto;
  box-shadow:
    6px 6px 12px $neu-2,
    -6px -6px 12px $white;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .loading-icon {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(white, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 充值记录区域
.history-section {
  margin-bottom: 40px;
}

.history-list {
  // 与其他区域保持一致的宽度
}

.empty-history {
  text-align: center;
  padding: 60px 20px;
  color: $text-muted;
  
  .empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background-color: $text-muted;
    border-radius: 50%;
    opacity: 0.3;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      background-color: $text-muted;
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'%3E%3Cpath d='M9 12l2 2 4-4'/%3E%3Cpath d='M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3'/%3E%3Cpath d='M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3'/%3E%3Cpath d='M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3'/%3E%3C/svg%3E");
      mask-size: contain;
      mask-repeat: no-repeat;
      mask-position: center;
    }
  }
  
  p {
    font-size: 16px;
    margin: 0;
    cursor: default;
    user-select: none;
  }
}

.history-table {
  background-color: $neu-1;
  border-radius: 16px;
  overflow: hidden;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  
  .table-header,
  .table-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 20px;
    padding: 20px 25px;
    align-items: center;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      
      .col-date,
      .col-status {
        order: 1;
      }
      
      .col-package,
      .col-amount {
        order: 2;
      }
    }
  }
  
  .table-header {
    background-color: rgba($purple, 0.1);
    font-weight: 700;
    color: $black;
    font-size: 16px;
  }
  
  .table-row {
    border-bottom: 1px solid rgba($text-muted, 0.1);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: rgba($purple, 0.05);
    }
    
    .col-date,
    .col-package,
    .col-amount {
      color: $text-primary;
      font-size: 14px;
      cursor: default;
      user-select: none;
    }
    
    .col-status {
      font-weight: 600;
      font-size: 14px;
      cursor: default;
      user-select: none;
      
      &.success {
        color: $success-color;
      }
      
      &.pending {
        color: $warning-color;
      }
      
      &.failed {
        color: $error-color;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .packages-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .money-content {
    padding: 15px;
  }
  
  .money-header {
    margin-bottom: 30px;
    
    .money-title {
      font-size: 32px;
    }
    
    .money-subtitle {
      font-size: 16px;
    }
  }
  
  .packages-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .package-card {
    padding: 25px 20px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .order-summary {
    padding: 25px 20px;
  }
}

@media (max-width: 480px) {
  .money-container {
    padding: 15px;
  }
  
  .money-header .money-title {
    font-size: 28px;
  }
  
  .payment-method {
    padding: 12px;
    gap: 12px;
    
    .payment-icon {
      width: 40px;
      height: 40px;
      
      img {
        width: 24px;
        height: 24px;
      }
    }
    
    .payment-info {
      .payment-name {
        font-size: 16px;
      }
      
      .payment-desc {
        font-size: 13px;
      }
    }
    
    .payment-radio {
      width: 18px;
      height: 18px;
      
      .radio-dot {
        width: 8px;
        height: 8px;
      }
    }
  }
  
  .package-price .amount {
    font-size: 36px;
  }
}