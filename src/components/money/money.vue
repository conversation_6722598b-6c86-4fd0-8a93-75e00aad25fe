<template>
  <div class="money-container">
    <div class="money-content">
      <!-- 页面标题 -->
      <div class="money-header">
        <h1 class="money-title">会员充值</h1>
        <p class="money-subtitle">选择适合您的套餐，享受更多专业功能</p>
      </div>

      <!-- 当前会员状态 -->
      <div class="current-status">
        <div class="status-card">
          <div class="status-info">
            <span class="status-label">当前套餐</span>
            <span class="status-value">{{ currentPlan.name }}</span>
          </div>
          <div class="status-info">
            <span class="status-label">剩余次数</span>
            <span class="status-value">{{ currentPlan.remaining }}/{{ currentPlan.total }}</span>
          </div>
          <div class="status-info">
            <span class="status-label">到期时间</span>
            <span class="status-value">{{ currentPlan.expireDate }}</span>
          </div>
        </div>
      </div>

      <!-- 套餐选择 -->
      <div class="packages-section">
        <h2 class="section-title">选择套餐</h2>
        <div class="packages-grid">
          <div 
            v-for="pkg in packages" 
            :key="pkg.id"
            class="package-card"
            :class="{ 
              'selected': selectedPackage === pkg.id, 
              'popular': pkg.popular,
              'enterprise': pkg.type === 'enterprise'
            }"
            @click="selectPackage(pkg.id)"
          >
            <div class="package-header">
              <div class="package-name">{{ pkg.name }}</div>
              <div v-if="pkg.popular" class="popular-badge">最受欢迎</div>
              <div v-if="pkg.type === 'enterprise'" class="enterprise-badge">企业专享</div>
            </div>
            <div class="package-price">
              <span class="currency">¥</span>
              <span class="amount">{{ pkg.price }}</span>
              <span class="period">/月</span>
            </div>
            <div class="package-features">
              <div class="feature-item">
                <span class="feature-icon check-icon"></span>
                <span class="feature-text">{{ pkg.usage }}</span>
              </div>
              <div v-for="feature in pkg.features" :key="feature" class="feature-item">
                <span class="feature-icon check-icon"></span>
                <span class="feature-text">{{ feature }}</span>
              </div>
            </div>
            <button class="package-btn" :class="{ 'selected': selectedPackage === pkg.id }">
              {{ selectedPackage === pkg.id ? '已选择' : '选择套餐' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 支付方式和订单确认 - 左右布局 -->
      <div v-if="selectedPackage" class="payment-order-section">
        <!-- 左侧：支付方式 -->
        <div class="payment-section">
          <div class="payment-wrapper">
            <h2 class="section-title">选择支付方式</h2>
            <div class="payment-methods">
              <div 
                v-for="method in paymentMethods" 
                :key="method.id"
                class="payment-method"
                :class="{ 'selected': selectedPayment === method.id }"
                @click="selectPayment(method.id)"
              >
                <div class="payment-icon">
                  <img :src="method.icon" :alt="method.name" />
                </div>
                <div class="payment-info">
                  <div class="payment-name">{{ method.name }}</div>
                  <div class="payment-desc">{{ method.description }}</div>
                </div>
                <div class="payment-radio">
                  <span class="radio-dot"></span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：订单确认 -->
        <div v-if="selectedPayment" class="order-section">
          <div class="order-summary">
            <h3 class="order-title">订单确认</h3>
            <div class="order-details">
              <div class="order-item">
                <span class="item-label">套餐名称</span>
                <span class="item-value">{{ getSelectedPackage().name }}</span>
              </div>
              <div class="order-item">
                <span class="item-label">套餐价格</span>
                <span class="item-value">¥{{ getSelectedPackage().price }}</span>
              </div>
              <div class="order-item">
                <span class="item-label">支付方式</span>
                <span class="item-value">{{ getSelectedPayment().name }}</span>
              </div>
              <div class="order-total">
                <span class="total-label">总计</span>
                <span class="total-amount">¥{{ getSelectedPackage().price }}</span>
              </div>
            </div>
            <button class="pay-btn" @click="processPay" :disabled="isProcessing">
              <span v-if="isProcessing" class="loading-icon"></span>
              {{ isProcessing ? '处理中...' : '立即支付' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 充值记录 -->
      <div class="history-section">
        <h2 class="section-title">充值记录</h2>
        <div class="history-list">
          <div v-if="rechargeHistory.length === 0" class="empty-history">
            <div class="empty-icon"></div>
            <p>暂无充值记录</p>
          </div>
          <div v-else class="history-table">
            <div class="table-header">
              <span class="col-date">日期</span>
              <span class="col-package">套餐</span>
              <span class="col-amount">金额</span>
              <span class="col-status">状态</span>
            </div>
            <div v-for="record in rechargeHistory" :key="record.id" class="table-row">
              <span class="col-date">{{ record.date }}</span>
              <span class="col-package">{{ record.packageName }}</span>
              <span class="col-amount">¥{{ record.amount }}</span>
              <span class="col-status" :class="record.status">{{ getStatusText(record.status) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 支付成功弹窗 -->
    <div class="success-overlay" :class="{ show: showSuccessDialog }" @click="closeSuccessDialog">
      <div class="success-dialog" @click.stop>
        <div class="success-icon"></div>
        <h3 class="success-title">支付成功</h3>
        <p class="success-message">恭喜您成功开通{{ getSelectedPackage().name }}，现在可以享受更多专业功能！</p>
        <button class="success-btn" @click="closeSuccessDialog">我知道了</button>
      </div>
    </div>

    <!-- 支付失败弹窗 -->
    <div class="error-overlay" :class="{ show: showErrorDialog }" @click="closeErrorDialog">
      <div class="error-dialog" @click.stop>
        <div class="error-icon"></div>
        <h3 class="error-title">支付失败</h3>
        <p class="error-message">{{ errorMessage }}</p>
        <button class="error-btn" @click="closeErrorDialog">我知道了</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ToastMixin, ModalMixin, StorageMixin } from '../../mixins/mixins.js'
import { MoneyComponent } from './money.js'

export default {
  ...MoneyComponent,
  mixins: [ToastMixin, ModalMixin, StorageMixin]
}
</script>

<style src="./money.scss" scoped></style>
<style src="../../communal/popupPoge.scss" scoped></style>