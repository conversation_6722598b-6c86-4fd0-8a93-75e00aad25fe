// 个人中心页面JavaScript逻辑

// Vue组件配置
export const HomeNestedComponent = {
  name: 'HomeNested',
  data() {
    return {
      personalInfo: {
        name: '',
        gender: '',
        phone: '',
        email: '',
        jobStatus: ''
      },
      // 修改手机号弹窗
      showPhoneDialog: false,
      phoneForm: {
        newPhone: '',
        verificationCode: ''
      },
      phoneCodeCountdown: 0,
      // phoneCodeTimer由TimerMixin管理
      
      // 修改邮箱弹窗
      showEmailDialog: false,
      emailForm: {
        newEmail: '',
        verificationCode: ''
      },
      emailCodeCountdown: 0,
      // emailCodeTimer由TimerMixin管理
      
      // 修改个人信息弹窗
      showInfoDialog: false,
      infoForm: {
        name: '',
        gender: '',
        jobStatus: ''
      },
      
      // 修改密码弹窗
      showPasswordDialog: false,
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordError: '',
      
      // 通知弹窗
      showNotification: false,
      notificationMessage: '',
      notificationTitle: '',
      notificationType: 'success',
      
      // 退出登录确认对话框
      showLogoutConfirm: false,
      
      // 路由切换确认对话框
      showRouteConfirm: false,
      pendingRoute: null, // 存储待跳转的路由
      
      // 行业分类数据
      industryCategories: [],
      resumeInfo: {
        advantages: '',
        workExperience: [],
        projectExperience: [],
        education: [],
        organizations: [],
        honors: '',
        skills: '',
        volunteer: ''
      },
      expandedModules: {
        advantages: false,
        workExperience: false,
        projectExperience: false,
        education: false,
        organizations: false,
        honors: false,
        skills: false,
        volunteer: false
      },
      // 添加数据备份，用于取消编辑时恢复
      backupResumeInfo: {}
    }
  },
  computed: {
    // 性别显示文本
    genderText() {
      if (this.personalInfo.gender === 'male') return '男';
      if (this.personalInfo.gender === 'female') return '女';
      return '';
    },
    
    // 求职状态显示文本
    jobStatusText() {
      switch (this.personalInfo.jobStatus) {
        case 'ready-graduate':
          return '离校-随时到岗';
        case 'current-student-month':
          return '在校-月内到岗';
        case 'current-student-opportunity':
          return '在校-考虑机会';
        case 'not-looking':
          return '不求职';
        default:
          return '';
      }
    },
    
    // 检查是否有模块处于编辑状态
    hasEditingModule() {
      return Object.values(this.expandedModules).some(expanded => expanded);
    }
  },
  async mounted() {
    console.log('HomeNested组件已挂载');
    
    // 设置Vue实例的全局引用
    window.homeNestedVueInstance = this;
    
    // 加载行业分类数据
    await this.loadIndustries();
    
    // 初始化数据
    if (window.initHomeNested) {
      window.initHomeNested();
    }
    
    // 同步数据，但不同步编辑状态
    this.syncDataFromGlobalExceptExpanded();
    
    // 重置所有编辑状态为关闭
    this.resetAllEditStates();
    
    // 确保在下一个tick中初始化日期范围
    this.$nextTick(() => {
      this.initializeDateRanges();
    });
  },
  beforeRouteLeave(to, from, next) {
    // 检查是否有模块处于编辑状态
    if (this.hasEditingModule) {
      // 存储待跳转的路由和next函数
      this.pendingRoute = { to, from, next };
      // 显示自定义确认对话框
      this.showRouteConfirm = true;
    } else {
      // 没有编辑状态，直接离开
      next();
    }
  },
  
  beforeUnmount() {
    // 清理全局引用
    window.homeNestedVueInstance = null;
    
    // 清理定时器 - 使用TimerMixin
    this.clearAllTimers();
    
    // 调用清理函数
    if (window.cleanupHomeNested) {
      window.cleanupHomeNested();
    }
  },
  methods: {
    // 展示函数已废弃：模板已改为未展开仅显示头部
    // 加载行业数据
    async loadIndustries() {
      try {
        const response = await import('../../assets/json/industries.json');
        this.industryCategories = response.default.categories || [];
        console.log('行业数据加载成功:', this.industryCategories.length, '个分类');
      } catch (error) {
        console.error('加载行业数据失败:', error);
        this.industryCategories = [];
      }
    },
    
    // 同步全局数据到Vue实例（排除编辑状态）
    syncDataFromGlobalExceptExpanded() {
      if (window.homeNestedData) {
        // 同步个人信息
        Object.assign(this.personalInfo, window.homeNestedData.personalInfo);
        // 同步简历信息
        Object.assign(this.resumeInfo, window.homeNestedData.resumeInfo);
        // 规范化数据结构
        this.normalizeResumeData();
        // 不同步expandedModules，保持本地状态
      }
    },

    // 规范化简历数据结构
    normalizeResumeData() {
      // 确保workExperience数组及其元素结构正确
      if (!Array.isArray(this.resumeInfo.workExperience)) {
        this.resumeInfo.workExperience = [];
      }
      // 过滤掉null/undefined元素，并确保剩余元素结构正确
      this.resumeInfo.workExperience = this.resumeInfo.workExperience
        .filter(item => item != null)
        .map(item => {
          if (typeof item !== 'object') item = {};
          if (!item.hasOwnProperty('company')) item.company = '';
          if (!item.hasOwnProperty('position')) item.position = '';
          if (!item.hasOwnProperty('industry')) item.industry = '';
          if (!item.hasOwnProperty('dateRange')) item.dateRange = [];
          if (!item.hasOwnProperty('startDate')) item.startDate = '';
          if (!item.hasOwnProperty('endDate')) item.endDate = '';
          if (!item.hasOwnProperty('description')) item.description = '';
          return item;
        });

      // 确保projectExperience数组及其元素结构正确
      if (!Array.isArray(this.resumeInfo.projectExperience)) {
        this.resumeInfo.projectExperience = [];
      }
      this.resumeInfo.projectExperience = this.resumeInfo.projectExperience
        .filter(item => item != null)
        .map(item => {
          if (typeof item !== 'object') item = {};
          if (!item.hasOwnProperty('name')) item.name = '';
          if (!item.hasOwnProperty('role')) item.role = '';
          if (!item.hasOwnProperty('dateRange')) item.dateRange = [];
          if (!item.hasOwnProperty('startDate')) item.startDate = '';
          if (!item.hasOwnProperty('endDate')) item.endDate = '';
          if (!item.hasOwnProperty('description')) item.description = '';
          if (!item.hasOwnProperty('achievements')) item.achievements = '';
          return item;
        });

      // 确保education数组及其元素结构正确
      if (!Array.isArray(this.resumeInfo.education)) {
        this.resumeInfo.education = [];
      }
      this.resumeInfo.education = this.resumeInfo.education
        .filter(item => item != null)
        .map(item => {
          if (typeof item !== 'object') item = {};
          if (!item.hasOwnProperty('school')) item.school = '';
          if (!item.hasOwnProperty('major')) item.major = '';
          if (!item.hasOwnProperty('degree')) item.degree = '';
          if (!item.hasOwnProperty('dateRange')) item.dateRange = [];
          if (!item.hasOwnProperty('startDate')) item.startDate = '';
          if (!item.hasOwnProperty('endDate')) item.endDate = '';
          if (!item.hasOwnProperty('description')) item.description = '';
          if (!item.hasOwnProperty('courses')) item.courses = '';
          if (!item.hasOwnProperty('experience')) item.experience = '';
          return item;
        });

      // 确保organizations数组及其元素结构正确
      if (!Array.isArray(this.resumeInfo.organizations)) {
        this.resumeInfo.organizations = [];
      }
      this.resumeInfo.organizations = this.resumeInfo.organizations
        .filter(item => item != null)
        .map(item => {
          if (typeof item !== 'object') item = {};
          if (!item.hasOwnProperty('name')) item.name = '';
          if (!item.hasOwnProperty('role')) item.role = '';
          if (!item.hasOwnProperty('dateRange')) item.dateRange = [];
          if (!item.hasOwnProperty('startDate')) item.startDate = '';
          if (!item.hasOwnProperty('endDate')) item.endDate = '';
          if (!item.hasOwnProperty('description')) item.description = '';
          return item;
        });

      // 确保字符串字段不为undefined
      if (typeof this.resumeInfo.advantages !== 'string') this.resumeInfo.advantages = '';
      if (typeof this.resumeInfo.honors !== 'string') this.resumeInfo.honors = '';
      if (typeof this.resumeInfo.skills !== 'string') this.resumeInfo.skills = '';
      if (typeof this.resumeInfo.volunteer !== 'string') this.resumeInfo.volunteer = '';
    },
    
    // 重置所有编辑状态
    resetAllEditStates() {
      this.expandedModules = {
        advantages: false,
        workExperience: false,
        projectExperience: false,
        education: false,
        organizations: false,
        honors: false,
        skills: false,
        volunteer: false
      };
    },
    
    // 初始化日期范围
    initializeDateRanges() {
      // 为工作经历初始化日期范围
      this.resumeInfo.workExperience.forEach(item => {
        if (!item.dateRange && item.startDate && item.endDate) {
          item.dateRange = [item.startDate, item.endDate];
        }
      });
      
      // 为项目经历初始化日期范围
      this.resumeInfo.projectExperience.forEach(item => {
        if (!item.dateRange && item.startDate && item.endDate) {
          item.dateRange = [item.startDate, item.endDate];
        }
      });
      
      // 为教育经历初始化日期范围
      this.resumeInfo.education.forEach(item => {
        if (!item.dateRange && item.startDate && item.endDate) {
          item.dateRange = [item.startDate, item.endDate];
        }
      });
    },
    
    // 展开/收起模块
    toggleModule(moduleName) {
      const wasExpanded = this.expandedModules[moduleName];
      this.expandedModules[moduleName] = !this.expandedModules[moduleName];
      
      // 如果正在展开某个模块，创建数据备份
      if (this.expandedModules[moduleName]) {
        this.createBackup(moduleName);
        if (window.homeNestedData && window.homeNestedData.expandedModules) {
          window.homeNestedData.expandedModules[moduleName] = true;
        }
      } else {
        // 如果收起模块，恢复原始数据（用户没有保存的情况）
        this.restoreBackup(moduleName);
      }
    },
    
    // 创建数据备份
    createBackup(moduleName) {
      if (moduleName === 'advantages' || moduleName === 'honors' || moduleName === 'skills') {
        // 简单字符串字段
        this.backupResumeInfo[moduleName] = this.resumeInfo[moduleName];
      } else {
        // 数组字段，需要深拷贝
        this.backupResumeInfo[moduleName] = JSON.parse(JSON.stringify(this.resumeInfo[moduleName]));
      }
    },
    
    // 恢复数据备份
    restoreBackup(moduleName) {
      if (this.backupResumeInfo.hasOwnProperty(moduleName)) {
        if (moduleName === 'advantages' || moduleName === 'honors' || moduleName === 'skills') {
          this.resumeInfo[moduleName] = this.backupResumeInfo[moduleName];
        } else {
          this.resumeInfo[moduleName] = JSON.parse(JSON.stringify(this.backupResumeInfo[moduleName]));
        }
        // 清除备份
        delete this.backupResumeInfo[moduleName];
      }
    },
    
    // 注意：showToast方法已移到ToastMixin中
    
    // 关闭通知
    closeNotification() {
      this.showNotification = false;
    },

    // 兼容模板调用的别名方法
    showPhoneEditDialog() { this.openPhoneDialog(); },
    showEmailEditDialog() { this.openEmailDialog(); },
    showInfoEditDialog() { this.openInfoDialog(); },
    showPasswordEditDialog() { this.openPasswordDialog(); },
    goToLogin() {
      // 如果在Electron环境中，先重置窗口大小
      if (window.electronAPI) {
        window.electronAPI.logoutReset();
      }
      // 跳转到登录页面
      this.$router.push('/login');
    },
    
    // 打开修改手机号弹窗
    openPhoneDialog() {
      this.showPhoneDialog = true;
      this.phoneForm.newPhone = '';
      this.phoneForm.verificationCode = '';
    },
    
    // 关闭修改手机号弹窗
    closePhoneDialog() {
      this.showPhoneDialog = false;
      this.resetPhoneCode();
    },

    // 打开/关闭修改邮箱弹窗
    openEmailDialog() {
      this.showEmailDialog = true;
      this.emailForm.newEmail = '';
      this.emailForm.verificationCode = '';
    },
    closeEmailDialog() {
      this.showEmailDialog = false;
      this.emailCodeCountdown = 0;
      this.clearTimer('emailCodeTimer'); // 使用TimerMixin
    },

    // 打开/关闭修改个人信息弹窗
    openInfoDialog() {
      this.showInfoDialog = true;
      this.infoForm = {
        name: this.personalInfo.name || '',
        gender: this.personalInfo.gender || '',
        jobStatus: this.personalInfo.jobStatus || ''
      };
    },
    closeInfoDialog() {
      this.showInfoDialog = false;
    },

    // 打开/关闭修改密码弹窗
    openPasswordDialog() {
      this.showPasswordDialog = true;
      this.passwordForm = { currentPassword: '', newPassword: '', confirmPassword: '' };
      this.passwordError = '';
    },
    closePasswordDialog() {
      this.showPasswordDialog = false;
      this.passwordError = '';
    },

    // 保存个人信息修改
    saveInfoChange() {
      try {
        // 验证必填字段
        if (!this.infoForm.name || !this.infoForm.gender || !this.infoForm.jobStatus) {
          this.showErrorToast('请填写完整的个人信息', '提示');
          return;
        }

        // 更新个人信息
        this.personalInfo.name = this.infoForm.name;
        this.personalInfo.gender = this.infoForm.gender;
        this.personalInfo.jobStatus = this.infoForm.jobStatus;

        // 同步到全局数据
        if (window.homeNestedData) {
          Object.assign(window.homeNestedData.personalInfo, this.personalInfo);
        }

        // 保存到本地存储 - 使用StorageMixin
        this.saveToStorage('personalInfo', this.personalInfo);
        
        this.closeInfoDialog();
        this.showSuccessToast('个人信息修改成功');
      } catch (error) {
        console.error('保存个人信息失败:', error);
        this.showErrorToast('保存失败，请稍后重试', '错误');
      }
    },
    
    // 发送手机验证码
    async sendPhoneCode() {
      if (!this.phoneForm.newPhone) {
        this.showErrorToast('请输入新手机号', '提示');
        return;
      }
      
      try {
        // 这里应该调用真实的发送验证码API
        console.log('发送手机验证码到:', this.phoneForm.newPhone);
        
        this.phoneCodeCountdown = 60;
        this.setTimer('phoneCodeTimer', () => {
          this.phoneCodeCountdown--;
          if (this.phoneCodeCountdown <= 0) {
            this.resetPhoneCode();
          }
        }, 1000);
        
        this.showSuccessToast('验证码已发送');
      } catch (error) {
        console.error('发送手机验证码失败:', error);
        this.showErrorToast('发送验证码失败，请稍后重试', '错误');
      }
    },
    
    // 重置手机验证码 - 使用TimerMixin
    resetPhoneCode() {
      this.phoneCodeCountdown = 0;
      this.clearTimer('phoneCodeTimer');
    },

    // 通用保存：写入本地存储并提示
    saveAllResumeInfo() {
      try {
        // 同步到全局数据再保存
        if (window.homeNestedData) {
          window.homeNestedData.resumeInfo = { ...window.homeNestedData.resumeInfo, ...this.resumeInfo };
        }
        // 使用StorageMixin保存
        this.saveToStorage('resumeInfo', window.homeNestedData ? window.homeNestedData.resumeInfo : this.resumeInfo);
        this.showSuccessToast('保存成功');
        return true;
      } catch (e) {
        console.error('保存失败:', e);
        this.showErrorToast('保存失败，请稍后重试', '错误');
        return false;
      }
    },

    // 各模块保存方法
    saveAdvantages() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('advantages');
        this.expandedModules.advantages = false;
      }
    },
    saveWorkExperience() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('workExperience');
        this.expandedModules.workExperience = false;
      }
    },
    saveProjectExperience() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('projectExperience');
        this.expandedModules.projectExperience = false;
      }
    },
    saveEducation() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('education');
        this.expandedModules.education = false;
      }
    },
    saveOrganizations() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('organizations');
        this.expandedModules.organizations = false;
      }
    },
    saveHonors() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('honors');
        this.expandedModules.honors = false;
      }
    },
    saveSkills() {
      if (this.saveAllResumeInfo()) {
        this.clearBackup('skills');
        this.expandedModules.skills = false;
      }
    },
    
    // 清除指定模块的备份
    clearBackup(moduleName) {
      if (this.backupResumeInfo.hasOwnProperty(moduleName)) {
        delete this.backupResumeInfo[moduleName];
      }
    },
    
    // 恢复所有备份数据
    restoreAllBackups() {
      Object.keys(this.backupResumeInfo).forEach(moduleName => {
        this.restoreBackup(moduleName);
      });
      // 关闭所有编辑状态
      this.resetAllEditStates();
    },
    
    // 确认修改手机号
    async confirmPhoneChange() {
      if (!this.phoneForm.newPhone || !this.phoneForm.verificationCode) {
        this.showErrorToast('请填写完整信息', '提示');
        return;
      }
      
      try {
        // 这里应该调用真实的修改手机号API
        const result = await window.updatePhone(this.phoneForm.newPhone, this.phoneForm.verificationCode);
        
        if (result.success) {
          this.personalInfo.phone = this.phoneForm.newPhone;
          this.closePhoneDialog();
          this.showSuccessToast('手机号修改成功');
        } else {
          this.showErrorToast(result.message || '修改失败', '错误');
        }
      } catch (error) {
        console.error('修改手机号失败:', error);
        this.showErrorToast('修改失败，请稍后重试', '错误');
      }
    },
    
    // 退出登录
    logout() {
      this.showLogoutConfirm = true;
    },
    
    // 确认退出登录
    confirmLogout() {
      this.showLogoutConfirm = false;
      // 清除登录状态
      localStorage.removeItem('userToken');
      localStorage.removeItem('userInfo');
      // 跳转到登录页
      this.$router.push('/login');
    },
    
    // 取消退出登录
    cancelLogout() {
      this.showLogoutConfirm = false;
    },
    
    // 确认路由切换
    confirmRouteChange() {
      this.showRouteConfirm = false;
      if (this.pendingRoute) {
        // 用户确认离开，恢复所有未保存的数据
        this.restoreAllBackups();
        // 执行路由跳转
        this.pendingRoute.next();
        // 清理待跳转路由
        this.pendingRoute = null;
      }
    },
    
    // 取消路由切换
    cancelRouteChange() {
      this.showRouteConfirm = false;
      if (this.pendingRoute) {
        // 用户取消离开，阻止路由跳转
        this.pendingRoute.next(false);
        // 清理待跳转路由
        this.pendingRoute = null;
      }
    },
    
    // 添加工作经历
    addWorkExperience() {
      this.resumeInfo.workExperience.push({
        company: '',
        position: '',
        dateRange: [],
        description: '',
        startDate: '',
        endDate: ''
      });
    },
    
    // 删除工作经历
    removeWorkExperience(index) {
      this.resumeInfo.workExperience.splice(index, 1);
    },
    
    // 添加项目经历
    addProjectExperience() {
      this.resumeInfo.projectExperience.push({
        name: '',
        role: '',
        dateRange: [],
        description: '',
        startDate: '',
        endDate: ''
      });
    },
    
    // 删除项目经历
    removeProjectExperience(index) {
      this.resumeInfo.projectExperience.splice(index, 1);
    },
    
    // 添加教育经历
    addEducation() {
      this.resumeInfo.education.push({
        school: '',
        major: '',
        degree: '',
        dateRange: [],
        description: '',
        startDate: '',
        endDate: ''
      });
    },
    
    // 删除教育经历
    removeEducation(index) {
      this.resumeInfo.education.splice(index, 1);
    },

    // 添加组织经历
    addOrganization() {
      this.resumeInfo.organizations.push({
        name: '',
        role: '',
        dateRange: [],
        description: '',
        startDate: '',
        endDate: ''
      });
    },

    // 删除组织经历
    removeOrganization(index) {
      this.resumeInfo.organizations.splice(index, 1);
    },

    // 更新工作经历日期范围
    updateWorkDuration(work, dateRange) {
      if (dateRange && dateRange.length === 2) {
        work.startDate = dateRange[0];
        work.endDate = dateRange[1];
        work.dateRange = dateRange;
      } else {
        work.startDate = '';
        work.endDate = '';
        work.dateRange = [];
      }
    },

    // 更新项目经历日期范围
    updateProjectDuration(project, dateRange) {
      if (dateRange && dateRange.length === 2) {
        project.startDate = dateRange[0];
        project.endDate = dateRange[1];
        project.dateRange = dateRange;
      } else {
        project.startDate = '';
        project.endDate = '';
        project.dateRange = [];
      }
    },

    // 更新教育经历日期范围
    updateEducationDuration(education, dateRange) {
      if (dateRange && dateRange.length === 2) {
        education.startDate = dateRange[0];
        education.endDate = dateRange[1];
        education.dateRange = dateRange;
      } else {
        education.startDate = '';
        education.endDate = '';
        education.dateRange = [];
      }
    },

    // 更新组织经历日期范围
    updateOrganizationDuration(organization, dateRange) {
      if (dateRange && dateRange.length === 2) {
        organization.startDate = dateRange[0];
        organization.endDate = dateRange[1];
        organization.dateRange = dateRange;
      } else {
        organization.startDate = '';
        organization.endDate = '';
        organization.dateRange = [];
      }
    },
    
    // 自动调整文本域高度
    autoResizeTextarea() {
      this.$nextTick(() => {
        const textareas = this.$el.querySelectorAll('textarea');
        textareas.forEach(textarea => {
          textarea.style.height = 'auto';
          textarea.style.height = textarea.scrollHeight + 'px';
        });
      });
    }
  },
  watch: {
    // 监听个人信息变化
    personalInfo: {
      handler(newVal) {
        if (window.homeNestedData) {
          Object.assign(window.homeNestedData.personalInfo, newVal);
        }
      },
      deep: true
    },
    
    // 监听简历信息变化
    resumeInfo: {
      handler(newVal) {
        if (window.homeNestedData) {
          Object.assign(window.homeNestedData.resumeInfo, newVal);
        }
      },
      deep: true
    },
    
    // 监听个人优势内容变化，自动调整高度
    'resumeInfo.advantages': {
      handler() {
        this.$nextTick(() => {
          this.autoResizeTextarea();
        });
      }
    }
  }
};

// 全局变量存储
window.homeNestedData = {
  // 个人信息数据
  personalInfo: {
    name: '',
    gender: '',
    phone: '',
    email: '',
    jobStatus: ''
  },
  
  // 简历信息数据
  resumeInfo: {
    advantages: '',
    workExperience: [],
    projectExperience: [],
    education: [],
    organizations: [],
    honors: '',
    skills: '',
    volunteer: ''
  },
  
  // 展开的模块状态
  expandedModules: {
    advantages: false,
    workExperience: false,
    projectExperience: false,
    education: false,
    organizations: false,
    honors: false,
    skills: false,
    volunteer: false
  }
};

// 初始化函数
window.initHomeNested = function() {
  console.log('个人中心页面初始化');
  initResumeData();
  loadDataFromStorage();
};

// 初始化简历数据
function initResumeData() {
  const resumeInfo = window.homeNestedData.resumeInfo;
  
  // 初始化各类经历数组
  const initTemplates = {
    workExperience: { company: '', position: '', duration: '', description: '' },
    projectExperience: { name: '', role: '', duration: '', description: '', achievements: '' },
    education: { school: '', major: '', degree: '', duration: '', courses: '', experience: '' },
    organizations: { name: '', role: '', duration: '', description: '' }
  };
  
  Object.keys(initTemplates).forEach(key => {
    if (resumeInfo[key].length === 0) {
      resumeInfo[key].push(initTemplates[key]);
    }
  });
}

// 标签页切换
window.switchTab = function(tab) {
  console.log('切换到标签页:', tab);
  window.homeNestedData.activeTab = tab;
  
  // 触发Vue组件更新
  if (window.homeNestedVueInstance) {
    window.homeNestedVueInstance.activeTab = tab;
  }
};

// 切换模块展开状态
window.toggleModule = function(moduleName) {
  console.log('切换模块:', moduleName);
  window.homeNestedData.expandedModules[moduleName] = !window.homeNestedData.expandedModules[moduleName];
  
  // 触发Vue组件更新
  if (window.homeNestedVueInstance) {
    window.homeNestedVueInstance.expandedModules[moduleName] = window.homeNestedData.expandedModules[moduleName];
  }
};

// 通用消息显示函数
function showMessage(type, title, content) {
  if (window.homeNestedVueInstance && window.homeNestedVueInstance.showNotificationDialog) {
    window.homeNestedVueInstance.showNotificationDialog(content, type, title);
  } else {
    alert(content);
  }
}

// 保存个人信息
window.savePersonalInfo = function() {
  console.log('保存个人信息');
  
  const { name, gender, phone, email } = window.homeNestedData.personalInfo;
  
  // 验证必填字段
  if (!name || !gender || !phone || !email) {
    showMessage('warning', '验证失败', '请填写完整的个人信息！');
    return false;
  }
  
  // 验证邮箱格式 - 使用FormValidationMixin
  if (!window.homeNestedVueInstance.validateEmail(email)) {
    showMessage('error', '格式错误', '请输入正确的邮箱格式！');
    return false;
  }
  
  // 验证手机号格式 - 使用FormValidationMixin
  if (!window.homeNestedVueInstance.validatePhone(phone)) {
    showMessage('error', '格式错误', '请输入正确的手机号格式！');
    return false;
  }
  
  savePersonalDataToStorage();
  return true; // 返回成功状态，不显示消息
};

// 通用确认函数
function showConfirm(title, content, callback) {
  if (window.homeNestedVueInstance && window.homeNestedVueInstance.showConfirm) {
    window.homeNestedVueInstance.showConfirm(title, content, callback);
  } else {
    const result = confirm(content);
    if (callback) callback(result);
  }
}

// 重置个人信息表单
window.resetPersonalForm = function() {
  console.log('重置个人信息表单');
  
  showConfirm('确认重置', '确定要重置表单吗？', (confirmed) => {
    if (confirmed) {
      window.homeNestedData.personalInfo = {
        name: '', gender: '', phone: '', email: '', jobStatus: ''
      };
      
      if (window.homeNestedVueInstance) {
        Object.assign(window.homeNestedVueInstance.personalInfo, window.homeNestedData.personalInfo);
      }
    }
  });
};

// 保存简历信息
window.saveResumeInfo = function() {
  console.log('保存简历信息');
  saveResumeDataToStorage();
  return true; // 返回成功状态，不显示消息
};

// 重置简历信息表单
window.resetResumeForm = function() {
  console.log('重置简历信息表单');
  
  showConfirm('确认重置', '确定要重置简历信息吗？', (confirmed) => {
    if (confirmed) {
      window.homeNestedData.resumeInfo = {
        advantages: '', workExperience: [], projectExperience: [],
        education: [], organizations: [], honors: '', skills: '', volunteer: ''
      };
      
      initResumeData();
      
      if (window.homeNestedVueInstance) {
        Object.assign(window.homeNestedVueInstance.resumeInfo, window.homeNestedData.resumeInfo);
      }
    }
  });
};

// 通用添加经历函数
function addExperience(type, template) {
  console.log(`添加${type}经历`);
  window.homeNestedData.resumeInfo[type].push(template);
  
  if (window.homeNestedVueInstance) {
    window.homeNestedVueInstance.resumeInfo[type] = [...window.homeNestedData.resumeInfo[type]];
  }
}

// 通用删除经历函数
function removeExperience(type, index, minCount = 1) {
  console.log(`删除${type}经历:`, index);
  
  if (window.homeNestedData.resumeInfo[type].length > minCount) {
    window.homeNestedData.resumeInfo[type].splice(index, 1);
    
    if (window.homeNestedVueInstance) {
      window.homeNestedVueInstance.resumeInfo[type] = [...window.homeNestedData.resumeInfo[type]];
    }
  } else {
    showMessage('warning', '操作限制', `至少需要保留一个${type}项！`);
  }
}

// 添加工作经历
window.addWorkExperience = function() {
  addExperience('workExperience', { company: '', position: '', duration: '', description: '' });
};

// 删除工作经历
window.removeWorkExperience = function(index) {
  removeExperience('workExperience', index);
};



// 添加教育经历
window.addEducation = function() {
  addExperience('education', { school: '', major: '', degree: '', duration: '', courses: '', experience: '' });
};

// 删除教育经历
window.removeEducation = function(index) {
  removeExperience('education', index);
};

// 注意：saveToStorage方法已移到StorageMixin中

// 保存个人信息到本地存储 - 使用StorageMixin
function savePersonalDataToStorage() {
  if (window.homeNestedVueInstance) {
    window.homeNestedVueInstance.saveToStorage('personalInfo', window.homeNestedData.personalInfo);
  }
}

// 保存简历信息到本地存储 - 使用StorageMixin
function saveResumeDataToStorage() {
  if (window.homeNestedVueInstance) {
    window.homeNestedVueInstance.saveToStorage('resumeInfo', window.homeNestedData.resumeInfo);
  }
}

// 从本地存储加载数据
function loadDataFromStorage() {
  try {
    // 加载个人信息
    const savedPersonalInfo = localStorage.getItem('personalInfo');
    if (savedPersonalInfo) {
      window.homeNestedData.personalInfo = JSON.parse(savedPersonalInfo);
      console.log('已从本地存储加载个人信息');
    }
    
    // 加载简历信息
    const savedResumeInfo = localStorage.getItem('resumeInfo');
    if (savedResumeInfo) {
      const parsedResumeInfo = JSON.parse(savedResumeInfo);
      
      // 默认简历结构
      const defaultResumeInfo = {
        advantages: '', workExperience: [], projectExperience: [],
        education: [], organizations: [], honors: '', skills: '', volunteer: ''
      };
      
      // 合并数据并确保数组字段正确性
      window.homeNestedData.resumeInfo = { ...defaultResumeInfo, ...parsedResumeInfo };
      
      // 验证数组字段
      ['workExperience', 'projectExperience', 'education', 'organizations'].forEach(field => {
        if (!Array.isArray(window.homeNestedData.resumeInfo[field])) {
          console.warn(`修复字段 ${field}，将其重置为空数组`);
          window.homeNestedData.resumeInfo[field] = [];
        }
      });
      
      console.log('已从本地存储加载简历信息');
    }
  } catch (error) {
    console.error('从本地存储加载数据失败:', error);
    // 重置为默认值
    window.homeNestedData.resumeInfo = {
      advantages: '', workExperience: [], projectExperience: [],
      education: [], organizations: [], honors: '', skills: '', volunteer: ''
    };
  }
}

// 修改密码函数
window.changePassword = function(passwordForm) {
  console.log('修改密码:', passwordForm);
  
  // 验证当前密码（模拟，实际应调用API）
  if (passwordForm.currentPassword !== '123456') {
    return { success: false, message: '当前密码错误！' };
  }
  
  try {
    console.log('调用API修改密码...');
    return { success: true, message: '密码修改成功！' };
  } catch (error) {
    console.error('密码修改失败:', error);
    return { success: false, message: '密码修改失败，请稍后重试！' };
  }
};

// 清理函数
window.cleanupHomeNested = function() {
  console.log('个人中心页面清理');
  // 这里可以添加页面清理逻辑
};

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', window.initHomeNested);
} else {
  window.initHomeNested();
}