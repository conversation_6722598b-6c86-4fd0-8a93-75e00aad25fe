// 个人中心页面样式
@use 'sass:color';

// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$gray: #a0a5a8;
$black: #181818;
$bg-input: #f1f5f9;
$border-light: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-muted: #94a3b8;
$error-color: #ef4444;
$accent-primary: #6366f1;
$accent-secondary: #4f46e5;
$transition: 0.3s;
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Element Plus日期选择器样式覆盖
:deep(.el-date-editor),
:deep(.el-date-editor.el-input__wrapper),
:deep(.el-date-editor--daterange.el-input__wrapper),
:deep(.el-range-editor) {
  height: 42px !important;
  min-height: 42px !important;
  max-height: 42px !important;
  width: auto !important;
  flex: 1;
}

// 个人中心容器
.personal-center-container {
  width: 100%;
  min-height: 100vh;
  background: $neu-1;
  position: relative;
  cursor: default;
}

// 主要内容区域
.main-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
  cursor: default;

}

// 个人信息卡片
.personal-info-card {
  background: white;
  border-radius: 20px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  
  .info-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px 40px;
    
    @media (max-width: 1024px) {
      padding: 28px 32px;
    }
    
    @media (max-width: 900px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
      padding: 24px 28px;
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
      padding: 24px;
    }
    
    @media (max-width: 480px) {
      padding: 20px 16px;
      gap: 20px;
    }
    
    .basic-info {
      flex: 1;
      
      .user-name {
        font-size: 28px;
        font-weight: 700;
        color: $text-primary;
        margin: 0 0 16px 0;
        line-height: 1.2;
        cursor: default;
        
        @media (max-width: 1024px) {
          font-size: 26px;
        }
        
        @media (max-width: 900px) {
          font-size: 24px;
        }
        
        @media (max-width: 768px) {
          font-size: 22px;
        }
        
        @media (max-width: 480px) {
          font-size: 20px;
          margin-bottom: 12px;
        }
      }
      
      .contact-details {
        display: flex;
        flex-direction: column;
        gap: 12px;
        
        .contact-row {
          display: flex;
          justify-content: space-between;
          gap: 40px;
          
          @media (max-width: 1024px) {
            gap: 20px;
          }
          
          @media (max-width: 900px) {
            flex-direction: column;
            gap: 12px;
          }
          
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 12px;
          }
        }
        
        .contact-item {
          display: flex;
          align-items: center;
          font-size: 16px;
          flex: 1;
          min-width: 0; // 允许收缩
          
          @media (max-width: 1024px) {
            font-size: 15px;
          }
          
          @media (max-width: 900px) {
            flex: none; // 垂直布局时不使用flex
          }
          
          .contact-label {
            color: $text-secondary;
            font-weight: 500;
            min-width: 80px;
            cursor: default;
            flex-shrink: 0; // 标签不收缩
            
            @media (max-width: 1024px) {
              min-width: 70px;
            }
          }
          
          .contact-value {
            color: $text-primary;
            font-weight: 500;
            cursor: default;
            flex: 1;
            min-width: 0; // 允许收缩
            word-break: break-all; // 长文本换行
            
            @media (max-width: 900px) {
              word-break: break-word; // 更好的换行方式
            }
            
            .link-btn {
              color: $accent-primary;
              background: none;
              border: none;
              cursor: pointer;
              font-size: inherit;
              font-weight: 500;
              text-decoration: underline;
              margin-left: 8px;
              padding: 0;
              transition: color 0.2s ease;
              
              &:hover {
                color: color.adjust($accent-primary, $lightness: -10%);
              }
              
              &:focus {
                outline: 2px solid rgba($accent-primary, 0.3);
                outline-offset: 1px;
                border-radius: 2px;
              }
            }
          }
        }
      }
    }
    
    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      min-width: 160px;
      
      @media (max-width: 1024px) {
        min-width: 140px;
      }
      
      @media (max-width: 900px) {
        flex-direction: row;
        min-width: auto;
        width: 100%;
        justify-content: space-around;
        gap: 16px;
      }
      
      @media (max-width: 768px) {
        flex-direction: row;
        min-width: auto;
        width: 100%;
        justify-content: space-around;
        gap: 12px;
      }
      
      @media (max-width: 600px) {
        flex-direction: column;
        gap: 12px;
      }
      
      @media (max-width: 480px) {
        flex-direction: column;
        gap: 10px;
      }
      
      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 16px;
        border: none;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all $transition ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid rgba($border-light, 0.8);
        color: $text-primary;
        text-align: center;
        white-space: nowrap; // 防止文字换行
        
        @media (max-width: 1024px) {
          font-size: 13px;
          padding: 10px 14px;
        }
        
        @media (max-width: 900px) {
          flex: 1;
          min-width: 0;
        }
        
        @media (max-width: 600px) {
          flex: none;
        }
        
        @media (max-width: 480px) {
          font-size: 12px;
          padding: 10px 12px;
        }
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: $shadow-md;
        }
        
        &.primary {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          border-color: transparent;
          
          &:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
          }
        }
        
        &.secondary {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
          border-color: transparent;
          
          &:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
          }
        }
        
        &.tertiary {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          color: white;
          border-color: transparent;
          
          &:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
          }
        }
        
        &.logout {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          color: white;
          border-color: transparent;
          
          &:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
          }
        }
      }
    }
  }
}

// 通用动画
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 简历展示区域
.resume-display-section {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;
  
  .resume-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      border-color: #cbd5e0;
    }
    
    // 个人优势、专业技能、所获荣誉卡片特殊样式
    &.personal-advantages,
    &.professional-skills,
    &.honors-certificates {
      .edit-content {
        .experience-edit-item {
          .edit-textarea {
            width: 100% !important;
            max-width: none !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 8px !important;
            padding: 12px !important;
            background: white !important;
            resize: none !important;
            min-height: 120px;
            overflow: hidden !important;
            font-size: 14px;
            line-height: 1.6;
            box-sizing: border-box !important;
            
            &:focus {
              outline: none !important;
              box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2) !important;
              border-color: #3182ce !important;
            }
            
            &::placeholder {
              color: #a0aec0;
            }
          }
        }
      }
    }
    
    .card-header-simple {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 16px;
      border-bottom: 1px solid #e2e8f0;
      background: #f8fafc;
      
      .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
        cursor: default;
      }
      
      .edit-icon-btn {
        min-width: 80px;
        height: 36px;
        border: 1px solid rgba(102, 126, 234, 0.3);
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 600;
        color: #667eea;
        text-align: center;
        padding: 0 12px;
        gap: 6px;
        backdrop-filter: blur(5px);
        
        img {
          width: 16px;
          height: 16px;
          opacity: 0.8;
          transition: all 0.3s ease;
          filter: brightness(0) saturate(100%) invert(45%) sepia(95%) saturate(1735%) hue-rotate(221deg) brightness(95%) contrast(86%);
        }
        
        &:hover {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          border-color: rgba(102, 126, 234, 0.5);
          
          img {
            opacity: 1;
            transform: scale(1.1);
          }
        }
      }
      
      .save-icon-btn {
        min-width: 80px;
        height: 36px;
        border: 1px solid rgba(16, 185, 129, 0.3);
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 600;
        color: #10b981;
        text-align: center;
        padding: 0 12px;
        gap: 6px;
        backdrop-filter: blur(5px);
        
        img {
          width: 16px;
          height: 16px;
          opacity: 0.8;
          transition: all 0.3s ease;
          filter: brightness(0) saturate(100%) invert(64%) sepia(50%) saturate(1945%) hue-rotate(115deg) brightness(91%) contrast(88%);
        }
        
        &:hover {
          background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
          border-color: rgba(16, 185, 129, 0.5);
          
          img {
            opacity: 1;
            transform: scale(1.1);
          }
        }
      }
    }
    
    // 卡片展示区域
    .card-display {
      padding: 24px;
      background: white;
      border-top: 1px solid #e2e8f0;
      
      .experience-item {
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e2e8f0;
        
        &:last-child {
          margin-bottom: 0;
          border-bottom: none;
        }
      }
      
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .title-left {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          flex: 1;
          
          .main-title {
            color: #1a1a1a;
            font-size: 18px;
            font-weight: 700;
            margin-right: 8px;
          }
          
          .separator {
            color: #666;
            margin: 0 6px;
            font-weight: 400;
          }
          
          .sub-info {
            color: #4a4a4a;
            font-size: 15px;
            font-weight: 500;
            margin-right: 8px;
          }
        }
        
        .title-right {
          .date-text {
            color: #4a5568;
            font-size: 13px;
            font-weight: 500;
            background: white;
            padding: 8px 16px;
            border-radius: 20px;
            white-space: nowrap;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          }
        }
        
        @media (max-width: 768px) {
          flex-direction: column;
          align-items: flex-start;
          
          .title-right {
            margin-top: 8px;
          }
        }
      }
      
      .content-section {
        .desc-line {
          margin-bottom: 12px;
          line-height: 1.6;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label-text {
            color: #2a2a2a;
            font-size: 14px;
            font-weight: 600;
            margin-right: 8px;
          }
          
          .content-text {
            color: #4a4a4a;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.7;
            white-space: pre-wrap;
            word-wrap: break-word;
          }
        }
        
        .content-text {
          color: #4a4a4a;
          font-size: 14px;
          font-weight: 400;
          line-height: 1.7;
          margin: 0;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      }
    }
    
    .card-content-simple {
      padding: 24px;
      
      // 展示态样式已废弃（模板不再渲染），以下规则清理删除
      
      .edit-content {
        .experience-edit-item {
          background: rgba($bg-input, 0.5);
          border-radius: 12px;
          padding: 16px;
          margin-bottom: 16px;
          border: 1px solid rgba($border-light, 0.5);
          
                      .experience-header-edit {
            display: grid;
            gap: 12px;
            margin-bottom: 12px;
            width: 100%;
            
            // 第一行：公司名称 + 行业选择 + 删除按钮
            &:first-child {
              grid-template-columns: 1fr 1fr auto;
            }
            
            // 第二行：职位名称 + 日期选择器 + 空白区域（为了对齐）
            &:nth-child(2) {
              grid-template-columns: 1fr 1fr auto;
              
              // 为第三列添加空白占位
              &::after {
                content: '';
                width: 32px; // 与删除按钮宽度一致
                height: 32px; // 与删除按钮高度一致
                grid-column: 3;
              }
            }
            
            .edit-input {
              width: 100%;
              height: 42px;
              padding: 0 14px;
              border: 1px solid $border-light;
              border-radius: 8px;
              font-size: 14px;
              transition: all $transition ease;
              box-sizing: border-box;
              
              &:focus {
                outline: none;
                border-color: $accent-primary;
                box-shadow: 0 0 0 2px rgba($accent-primary, 0.2);
              }
            }
            
            .edit-select {
              flex: 1;
              width: 100%;
              height: 42px;
              padding: 0 14px;
              border: 1px solid $border-light;
              border-radius: 8px;
              font-size: 14px;
              background: white;
              transition: all $transition ease;
              box-sizing: border-box;
              cursor: pointer;
              appearance: none;
              -webkit-appearance: none;
              -moz-appearance: none;
              
              &:focus {
                outline: none;
                border-color: $accent-primary;
                box-shadow: 0 0 0 2px rgba($accent-primary, 0.2);
              }
              
              option {
                padding: 8px;
              }
            }
            
            // Element Plus 日期选择器样式 - 自适应宽度
            .date-range-picker {
              flex: 1;
              width: auto !important;
              min-width: 0 !important;
              height: 42px;
              
              :deep(.el-input__wrapper) {
                height: 42px !important;
                min-height: 42px !important;
                max-height: 42px !important;
                width: auto !important;
                min-width: 0 !important;
                flex: 1;
                border-radius: 8px;
                border: 1px solid $border-light;
                transition: all $transition ease;
                padding: 0 14px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                
                &:hover {
                  border-color: rgba($accent-primary, 0.5);
                }
                
                &.is-focus {
                  border-color: $accent-primary;
                  box-shadow: 0 0 0 2px rgba($accent-primary, 0.2);
                }
              }
              
              :deep(.el-range-editor) {
                height: 42px !important;
                min-height: 42px !important;
                max-height: 42px !important;
                width: auto !important;
                min-width: 0 !important;
                flex: 1;
                display: flex;
                align-items: center;
                padding: 0;
                box-sizing: border-box;
              }


              :deep(.el-tooltip__trigger) {
                height: 42px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
              }

              :deep(.el-date-editor) {
                height: 42px;
                box-sizing: border-box;
              }

              :deep(.el-range-input) {
                height: 20px !important;
                line-height: 20px !important;
                font-size: 14px;
                border: none !important;
                padding: 0 !important;
                background: transparent !important;
                flex: 1;
              }

              :deep(.el-range-separator) {
                color: $text-secondary;
                font-weight: 500;
                padding: 0 8px;
                font-size: 14px;
                height: 20px !important;
                line-height: 20px !important;
              }

              :deep(.el-range__icon),
              :deep(.el-range__close-icon) {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 16px !important;
                height: 16px !important;
                margin: 0 4px;
              }
            }
            
            .remove-btn {
              width: 32px;
              height: 32px;
              background: rgba($error-color, 0.1);
              color: $error-color;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-size: 16px;
              transition: all $transition ease;
              
              &:hover {
                background: rgba($error-color, 0.2);
              }
            }
          }
          
          .experience-details-edit {
            display: flex;
            flex-direction: column;
            gap: 12px;
            
            .edit-input {
              padding: 10px 14px;
              border: 1px solid $border-light;
              border-radius: 8px;
              font-size: 14px;
              transition: all $transition ease;
              
              &:focus {
                outline: none;
                border-color: $accent-primary;
                box-shadow: 0 0 0 2px rgba($accent-primary, 0.2);
              }
            }
            
            .edit-textarea {
              padding: 12px 14px;
              border: 1px solid $border-light;
              border-radius: 8px;
              font-size: 14px;
              resize: none;
              min-height: 80px;
              overflow: hidden;
              font-family: inherit;
              transition: all $transition ease;
              
              &:focus {
                outline: none;
                border-color: $accent-primary;
                box-shadow: 0 0 0 2px rgba($accent-primary, 0.2);
              }
            }
          }
        }
        

        
        .add-btn {
          width: 100%;
          padding: 12px;
          background: #f0fff4;
          color: #38a169;
          border: 1px dashed #38a169;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 16px;
          
          &:hover {
            background: #e6fffa;
            border-color: #2f855a;
          }
        }
        
        .edit-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
          
          .save-btn {
            background: #3182ce;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              background: #2c5282;
            }
          }
          
          .cancel-btn {
            background: #f7fafc;
            color: #718096;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              background: #edf2f7;
              border-color: #cbd5e0;
            }
          }
        }
      }
      

    }
  }
}

.edit-dialog {
  background: white;
  border-radius: 20px;
  box-shadow: $shadow-xl;
  border: 1px solid rgba($border-light, 0.6);
  width: 480px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all $transition ease;
  
  .edit-dialog-overlay.show & {
    transform: scale(1);
  }
  
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 1px solid rgba($border-light, 0.5);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    
    h3 {
      font-size: 20px;
      font-weight: 700;
      color: $text-primary;
      margin: 0;
      cursor: default;

    }
    
    .close-btn {
      background: rgba($text-muted, 0.1);
      border: none;
      font-size: 18px;
      color: $text-muted;
      cursor: pointer;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      transition: all $transition ease;
      
      &:hover {
        background: rgba($text-muted, 0.2);
        color: $text-primary;
      }
    }
  }
  
  .dialog-content {
    padding: 32px;
    overflow-y: auto;
    
    .form-group {
      margin-bottom: 20px;
      
      .form-label {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 8px;
        cursor: default;

      }
      
      .form-input, .form-select {
        width: 200px;
        height: 42px;
        padding: 8px 16px;
        border: 1px solid $border-light;
        border-radius: 12px;
        font-size: 14px;
        background: white;
        color: $text-primary;
        transition: all $transition ease;
        box-sizing: border-box;
      }
      
      .verification-form-input {
        width: 100%;
        max-width: 300px;
        height: 42px;
        padding: 8px 16px;
        border: 1px solid $border-light;
        border-radius: 12px;
        font-size: 14px;
        background: white;
        color: $text-primary;
        transition: all $transition ease;
        box-sizing: border-box;
        
        &:focus {
          outline: none;
          border-color: $accent-primary;
          box-shadow: 0 0 0 3px rgba($accent-primary, 0.1);
          background: white;
        }
        
        &::placeholder {
          color: $text-muted;
        }
      }
      
      .form-select {
        cursor: pointer;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        // 移除原生的箭头背景图片，因为SelectBox组件有自己的箭头
        // background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2'%3E%3Cpolyline points='6,9 12,15 18,9'/%3E%3C/svg%3E");
        // background-repeat: no-repeat;
        // background-position: right 12px center;
        // background-size: 18px;
        // padding-right: 40px;
        border: 2px solid rgba($accent-primary, 0.3);
        
        &:hover {
          border-color: rgba($accent-primary, 0.5);
          background-color: rgba($accent-primary, 0.02);
        }
        
        &:focus {
          border-color: $accent-primary;
          box-shadow: 0 0 0 3px rgba($accent-primary, 0.15);
          background-color: white;
        }
        
        option {
          padding: 8px 12px;
          background: white;
          color: $text-primary;
          
          &:hover {
            background: rgba($accent-primary, 0.1);
          }
        }
      }
    }
    
    .form-row {
      display: flex;
      gap: 16px;
      
      @media (max-width: 600px) {
        flex-direction: column;
        gap: 0;
      }
      
      .form-group {
        flex: 1;
      }
    }
    
    .verification-input-group {
      display: flex;
      gap: 12px;
      align-items: flex-end;
      
              .form-input {
          flex: 1;
          height: 42px;
          padding: 8px 16px;
          border: 1px solid $border-light;
          border-radius: 12px;
          font-size: 14px;
          background: white;
          color: $text-primary;
          transition: all $transition ease;
          box-sizing: border-box;
        
        &:focus {
          outline: none;
          border-color: $accent-primary;
          box-shadow: 0 0 0 3px rgba($accent-primary, 0.1);
          background: white;
        }
        
        &::placeholder {
          color: $text-muted;
        }
      }
      
      .send-code-btn {
        background: linear-gradient(135deg, $accent-primary 0%, $accent-secondary 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all $transition ease;
        white-space: nowrap;
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, $accent-secondary 0%, #3730a3 100%);
          transform: translateY(-1px);
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
    
    .error-message {
      color: $error-color;
      font-size: 13px;
      margin-top: 8px;
      padding: 8px 12px;
      background: rgba($error-color, 0.1);
      border-radius: 8px;
      border: 1px solid rgba($error-color, 0.2);
      cursor: default;

    }
    
    .dialog-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid rgba($border-light, 0.5);
      
      .btn-primary, .btn-secondary {
        padding: 12px 24px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all $transition ease;
        border: none;
        
        &:hover {
          transform: translateY(-1px);
        }
      }
      
      .btn-primary {
        background: linear-gradient(135deg, $accent-primary 0%, $accent-secondary 100%);
        color: white;
        
        &:hover {
          background: linear-gradient(135deg, $accent-secondary 0%, #3730a3 100%);
          box-shadow: $shadow-md;
        }
      }
      
      .btn-secondary {
        background: rgba($text-muted, 0.1);
        color: $text-muted;
        border: 1px solid rgba($text-muted, 0.2);
        
        &:hover {
          background: rgba($text-muted, 0.2);
          color: $text-primary;
        }
      }
    }
  }
  

    

    

    

  }

// 通用弹窗样式
.edit-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all $transition ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// 通知弹窗样式
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

.popup-dialog {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 40px 30px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 0 48px 24px rgba(255, 255, 255, 0.55);
  transform: scale(0.9);
  transition: transform 0.3s ease;
  
  .popup-overlay.show & {
    transform: scale(1);
  }
}


// 全局覆盖：确保支付成功/失败弹窗内容与按钮居中（不改动 popupPoge.scss）
:global(.success-dialog),
:global(.error-dialog) {
  text-align: center;
}

:global(.success-title),
:global(.error-title),
:global(.success-message),
:global(.error-message) {
  text-align: center;
}

:global(.success-btn),
:global(.error-btn) {
  display: inline-block;
  margin: 0 auto;
}


.popup-title {
  font-size: 24px;
  font-weight: 700;
  color: $black;
  margin-bottom: 15px;
  cursor: default;
  user-select: none;
  text-align: center;
}

.popup-message {
  font-size: 16px;
  color: $gray;
  line-height: 1.6;
  margin-bottom: 30px;
  cursor: default;
  user-select: none;
  text-align: center;
}

.popup-btn {
  padding: 14px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    6px 6px 12px $neu-2,
    -6px -6px 12px $white;
  outline: none;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.success {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
  }
  
  &.error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
  }
  
  &.warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
  }
  
  &.info {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
  }
  
  &.secondary {
    background: rgba($text-muted, 0.1);
    color: $text-muted;
    border: 1px solid rgba($text-muted, 0.3);
    
    &:hover {
      background: rgba($text-muted, 0.2);
      color: $text-primary;
    }
  }
}

// 确认对话框样式
.confirm-dialog {
  .confirm-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 20px;
    
    .popup-btn {
      min-width: 100px;
      
      &.secondary {
        &:hover {
          transform: translateY(-2px);
        }
      }
      
      &.warning {
        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }
}

