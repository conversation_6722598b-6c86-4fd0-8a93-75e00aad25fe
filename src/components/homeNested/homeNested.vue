<template>
  <div class="personal-center-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 个人信息卡片 -->
      <div class="personal-info-card">
        <div class="info-content">
          <div class="basic-info">
            <h1 class="user-name">{{ personalInfo.name || '未设置姓名' }}</h1>
            <div class="contact-details">
              <div class="contact-row">
                <div class="contact-item">
                  <span class="contact-label">性别：</span>
                  <span class="contact-value">{{ genderText || '未设置' }}</span>
                </div>
                <div class="contact-item">
                  <span class="contact-label">手机号：</span>
                  <span v-if="personalInfo.phone" class="contact-value">{{ personalInfo.phone }}</span>
                  <span v-else class="contact-value">
                    未填写
                    <button class="link-btn" @click="showPhoneEditDialog">添加</button>
                  </span>
                </div>
              </div>
              <div class="contact-row">
                <div class="contact-item">
                  <span class="contact-label">求职状态：</span>
                  <span class="contact-value">{{ jobStatusText || '未设置' }}</span>
                </div>
                <div class="contact-item">
                  <span class="contact-label">邮箱：</span>
                  <span v-if="personalInfo.email" class="contact-value">{{ personalInfo.email }}</span>
                  <span v-else class="contact-value">
                    未填写
                    <button class="link-btn" @click="showEmailEditDialog">添加</button>
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="action-buttons">
            <button class="action-btn logout" @click="goToLogin">
              <span>退出登录</span>
            </button>
            <button class="action-btn secondary" @click="showInfoEditDialog">
              <span>修改个人信息</span>
            </button>
            <button class="action-btn tertiary" @click="showPasswordEditDialog">
              <span>修改密码</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 简历信息展示区域 -->
      <div class="resume-display-section">
        <!-- 个人优势卡片 -->
        <div class="resume-card personal-advantages">
          <div class="card-header-simple">
            <h3 class="card-title">个人优势</h3>
            <button class="edit-icon-btn" @click="toggleModule('advantages')" title="编辑" v-if="!expandedModules.advantages">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveAdvantages" title="保存" v-if="expandedModules.advantages">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.advantages && resumeInfo.advantages">
            <div class="content-section">
              <p class="content-text">{{ resumeInfo.advantages }}</p>
            </div>
          </div>
          <div class="card-content-simple" v-if="expandedModules.advantages">
            <div class="edit-content">
              <div class="experience-edit-item">
                <textarea 
                  v-model="resumeInfo.advantages" 
                  class="edit-textarea" 
                  placeholder="请描述您的个人优势和特长..."
                  rows="4"
                  @input="autoResizeTextarea($event.target)"
                  ref="advantagesTextarea"
                ></textarea>
              </div>

            </div>
          </div>
        </div>



        <!-- 工作/实习经历卡片 -->
        <div class="resume-card work-experience">
          <div class="card-header-simple">
            <h3 class="card-title">工作/实习经历</h3>
            <button class="edit-icon-btn" @click="toggleModule('workExperience')" title="编辑" v-if="!expandedModules.workExperience">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveWorkExperience" title="保存" v-if="expandedModules.workExperience">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.workExperience && resumeInfo.workExperience && resumeInfo.workExperience.length > 0 && resumeInfo.workExperience.some(w => w && (w.company || w.position))">
            <template v-for="(work, index) in resumeInfo.workExperience" :key="index">
              <div v-if="work && (work.company || work.position)" class="experience-item">
                <div class="title-row">
                  <div class="title-left">
                    <span class="main-title">{{ work.company || '未填写公司' }}</span>
                    <span v-if="work.industry" class="separator">-</span>
                    <span v-if="work.industry" class="sub-info">{{ work.industry }}</span>
                    <span v-if="work.position" class="separator">-</span>
                    <span v-if="work.position" class="sub-info">{{ work.position }}</span>
                </div>
                  <div class="title-right" v-if="work.startDate && work.endDate">
                    <span class="date-text">{{ work.startDate.replace('-', '.') }}---{{ work.endDate.replace('-', '.') }}</span>
              </div>
            </div>
                <div class="content-section" v-if="work.description">
                  <div class="desc-line">
                    <span class="label-text">工作描述：</span>
                    <span class="content-text">{{ work.description }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div class="card-content-simple" v-if="expandedModules.workExperience">
            <div class="edit-content">
              <div v-for="(work, index) in resumeInfo.workExperience" :key="index" class="experience-edit-item">
                <div class="experience-header-edit">
                  <input v-model="work.company" class="edit-input" placeholder="公司名称" />
                  <SelectBox
                    v-model="work.industry"
                    :options="flatIndustryOptions"
                    placeholder="请选择所在行业"
                    custom-class="edit-select"
                  />
                  <button type="button" class="remove-btn" @click="removeWorkExperience(index)">×</button>
                </div>
                <div class="experience-header-edit">
                  <input v-model="work.position" class="edit-input" placeholder="职位名称" />
                  <el-date-picker
                    v-model="work.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="date-range-picker"
                    :clearable="true"
                    :editable="false"
                    @change="updateWorkDuration(work, $event)"
                  />
                </div>
                <div class="experience-details-edit">
                  <textarea 
                    v-model="work.description" 
                    class="edit-textarea" 
                    placeholder="工作内容/业绩..." 
                    rows="3"
                    @input="autoResizeTextarea($event.target)"
                  ></textarea>
                </div>
              </div>
              <button type="button" class="add-btn" @click="addWorkExperience">+ 添加工作经历</button>

            </div>
          </div>
        </div>

        <!-- 项目经历卡片 -->
        <div class="resume-card project-experience">
          <div class="card-header-simple">
            <h3 class="card-title">项目经历</h3>
            <button class="edit-icon-btn" @click="toggleModule('projectExperience')" title="编辑" v-if="!expandedModules.projectExperience">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveProjectExperience" title="保存" v-if="expandedModules.projectExperience">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.projectExperience && resumeInfo.projectExperience && resumeInfo.projectExperience.length > 0 && resumeInfo.projectExperience.some(p => p && (p.name || p.role))">
            <template v-for="(project, index) in resumeInfo.projectExperience" :key="index">
              <div v-if="project && (project.name || project.role)" class="experience-item">
                <div class="title-row">
                  <div class="title-left">
                    <span class="main-title">{{ project.name || '未填写项目名称' }}</span>
                    <span v-if="project.role" class="separator">-</span>
                    <span v-if="project.role" class="sub-info">{{ project.role }}</span>
                </div>
                  <div class="title-right" v-if="project.startDate && project.endDate">
                    <span class="date-text">{{ project.startDate.replace('-', '.') }}---{{ project.endDate.replace('-', '.') }}</span>
              </div>
            </div>
                <div class="content-section">
                  <div class="desc-line" v-if="project.description">
                    <span class="label-text">项目描述：</span>
                    <span class="content-text">{{ project.description }}</span>
                  </div>
                  <div class="desc-line" v-if="project.achievements">
                    <span class="label-text">项目业绩：</span>
                    <span class="content-text">{{ project.achievements }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div class="card-content-simple" v-if="expandedModules.projectExperience">
            <div class="edit-content">
              <div v-for="(project, index) in resumeInfo.projectExperience" :key="index" class="experience-edit-item">
                <div class="experience-header-edit">
                  <input v-model="project.name" class="edit-input" placeholder="项目名称" />
                  <input v-model="project.role" class="edit-input" placeholder="担任角色" />
                  <button type="button" class="remove-btn" @click="removeProjectExperience(index)">×</button>
                </div>
                <div class="experience-header-edit">
                  <el-date-picker
                    v-model="project.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="date-range-picker"
                    :clearable="true"
                    :editable="false"
                    @change="updateProjectDuration(project, $event)"
                  />
                </div>
                <div class="experience-details-edit">
                  <textarea 
                    v-model="project.description" 
                    class="edit-textarea" 
                    placeholder="项目描述..." 
                    rows="3"
                    @input="autoResizeTextarea($event.target)"
                  ></textarea>
                </div>
                <div class="experience-details-edit">
                  <textarea 
                    v-model="project.achievements" 
                    class="edit-textarea" 
                    placeholder="项目业绩..." 
                    rows="2"
                    @input="autoResizeTextarea($event.target)"
                  ></textarea>
                </div>
              </div>
              <button type="button" class="add-btn" @click="addProjectExperience">+ 添加项目经历</button>

            </div>
          </div>
        </div>

        <!-- 教育经历卡片 -->
        <div class="resume-card education-experience">
          <div class="card-header-simple">
            <h3 class="card-title">教育经历</h3>
            <button class="edit-icon-btn" @click="toggleModule('education')" title="编辑" v-if="!expandedModules.education">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveEducation" title="保存" v-if="expandedModules.education">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.education && resumeInfo.education && resumeInfo.education.length > 0 && resumeInfo.education.some(e => e && (e.school || e.major))">
            <template v-for="(edu, index) in resumeInfo.education" :key="index">
              <div v-if="edu && (edu.school || edu.major)" class="experience-item">
                <div class="title-row">
                  <div class="title-left">
                    <span class="main-title">{{ edu.school || '未填写学校' }}</span>
                    <span v-if="edu.major" class="separator">-</span>
                    <span v-if="edu.major" class="sub-info">{{ edu.major }}</span>
                    <span v-if="edu.degree" class="separator">-</span>
                    <span v-if="edu.degree" class="sub-info">{{ edu.degree }}</span>
                </div>
                  <div class="title-right" v-if="edu.startDate && edu.endDate">
                    <span class="date-text">{{ edu.startDate.replace('-', '.') }}---{{ edu.endDate.replace('-', '.') }}</span>
              </div>
            </div>
                <div class="content-section">
                  <div class="desc-line" v-if="edu.courses">
                    <span class="label-text">主修课程：</span>
                    <span class="content-text">{{ edu.courses }}</span>
                  </div>
                  <div class="desc-line" v-if="edu.experience">
                    <span class="label-text">在校经历：</span>
                    <span class="content-text">{{ edu.experience }}</span>
                  </div>
                  <div class="desc-line" v-if="edu.description">
                    <span class="label-text">学习描述：</span>
                    <span class="content-text">{{ edu.description }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div class="card-content-simple" v-if="expandedModules.education">
            <div class="edit-content">
              <div v-for="(edu, index) in resumeInfo.education" :key="index" class="experience-edit-item">
                <div class="experience-header-edit">
                  <input v-model="edu.school" class="edit-input" placeholder="学校名称" />
                  <SelectBox
                    v-model="edu.degree"
                    :options="degreeOptions"
                    placeholder="请选择学历"
                    custom-class="edit-select"
                  />
                  <button type="button" class="remove-btn" @click="removeEducation(index)">×</button>
                </div>
                <div class="experience-header-edit">
                  <input v-model="edu.major" class="edit-input" placeholder="专业名称" />
                  <el-date-picker
                    v-model="edu.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="入学日期"
                    end-placeholder="毕业日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="date-range-picker"
                    :clearable="true"
                    :editable="false"
                    @change="updateEducationDuration(edu, $event)"
                  />
                </div>
                <div class="experience-details-edit">
                  <textarea 
                    v-model="edu.courses" 
                    class="edit-textarea" 
                    placeholder="主修课程..." 
                    rows="2"
                    @input="autoResizeTextarea($event.target)"
                  ></textarea>
                </div>
                <div class="experience-details-edit">
                  <textarea 
                    v-model="edu.experience" 
                    class="edit-textarea" 
                    placeholder="在校经历..." 
                    rows="3"
                    @input="autoResizeTextarea($event.target)"
                  ></textarea>
                </div>
              </div>
              <button type="button" class="add-btn" @click="addEducation">+ 添加教育经历</button>

            </div>
          </div>
        </div>

        <!-- 社团/组织经历卡片 -->
        <div class="resume-card organization-experience">
          <div class="card-header-simple">
            <h3 class="card-title">社团/组织经历</h3>
            <button class="edit-icon-btn" @click="toggleModule('organizations')" title="编辑" v-if="!expandedModules.organizations">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveOrganizations" title="保存" v-if="expandedModules.organizations">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.organizations && resumeInfo.organizations && resumeInfo.organizations.length > 0 && resumeInfo.organizations.some(o => o && (o.name || o.role))">
            <template v-for="(org, index) in resumeInfo.organizations" :key="index">
              <div v-if="org && (org.name || org.role)" class="experience-item">
                <div class="title-row">
                  <div class="title-left">
                    <span class="main-title">{{ org.name || '未填写组织名称' }}</span>
                    <span v-if="org.role" class="separator">-</span>
                    <span v-if="org.role" class="sub-info">{{ org.role }}</span>
                </div>
                  <div class="title-right" v-if="org.startDate && org.endDate">
                    <span class="date-text">{{ org.startDate.replace('-', '.') }}---{{ org.endDate.replace('-', '.') }}</span>
              </div>
            </div>
                <div class="content-section" v-if="org.description">
                  <div class="desc-line">
                    <span class="label-text">组织描述：</span>
                    <span class="content-text">{{ org.description }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div class="card-content-simple" v-if="expandedModules.organizations">
            <div class="edit-content">
              <div v-for="(org, index) in resumeInfo.organizations" :key="index" class="experience-edit-item">
                <div class="experience-header-edit">
                  <input v-model="org.name" class="edit-input" placeholder="社团/组织名称" />
                  <input v-model="org.role" class="edit-input" placeholder="担任角色" />
                  <button type="button" class="remove-btn" @click="removeOrganization(index)">×</button>
                </div>
                <div class="experience-header-edit">
                  <el-date-picker
                    v-model="org.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="date-range-picker"
                    :clearable="true"
                    :editable="false"
                    @change="updateOrganizationDuration(org, $event)"
                  />
                </div>
                <div class="experience-details-edit">
                  <textarea 
                    v-model="org.description" 
                    class="edit-textarea" 
                    placeholder="经历描述..." 
                    rows="3"
                    @input="autoResizeTextarea($event.target)"
                  ></textarea>
                </div>
              </div>
              <button type="button" class="add-btn" @click="addOrganization">+ 添加社团/组织经历</button>

            </div>
          </div>
        </div>

        <!-- 所获荣誉/证书卡片 -->
        <div class="resume-card honors-certificates">
          <div class="card-header-simple">
            <h3 class="card-title">所获荣誉/证书</h3>
            <button class="edit-icon-btn" @click="toggleModule('honors')" title="编辑" v-if="!expandedModules.honors">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveHonors" title="保存" v-if="expandedModules.honors">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.honors && resumeInfo.honors">
            <div class="content-section">
              <p class="content-text">{{ resumeInfo.honors }}</p>
            </div>
          </div>
          <div class="card-content-simple" v-if="expandedModules.honors">
            <div class="edit-content">
              <div class="experience-edit-item">
                <textarea 
                  v-model="resumeInfo.honors" 
                  class="edit-textarea" 
                  placeholder="请输入您的荣誉/证书，每行一个..."
                  rows="4"
                  @input="autoResizeTextarea($event.target)"
                  ref="honorsTextarea"
                ></textarea>
              </div>

            </div>
          </div>
        </div>

        <!-- 专业技能卡片 -->
        <div class="resume-card professional-skills">
          <div class="card-header-simple">
            <h3 class="card-title">专业技能</h3>
            <button class="edit-icon-btn" @click="toggleModule('skills')" title="编辑" v-if="!expandedModules.skills">
              <img src="/icons/bianji.png" alt="编辑" />
              编辑
            </button>
            <button class="save-icon-btn" @click="saveSkills" title="保存" v-if="expandedModules.skills">
              <img src="/icons/chenggong.png" alt="保存" />
              保存
            </button>
          </div>
                    <!-- 显示完整内容 -->
          <div class="card-display" v-if="!expandedModules.skills && resumeInfo.skills">
            <div class="content-section">
              <p class="content-text">{{ resumeInfo.skills }}</p>
            </div>
          </div>
          <div class="card-content-simple" v-if="expandedModules.skills">
            <div class="edit-content">
              <div class="experience-edit-item">
                <textarea 
                  v-model="resumeInfo.skills" 
                  class="edit-textarea" 
                  placeholder="请描述您的专业技能..."
                  rows="4"
                  @input="autoResizeTextarea($event.target)"
                  ref="skillsTextarea"
                ></textarea>
              </div>

            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- 修改手机号弹窗 -->
    <div class="edit-dialog-overlay" :class="{ show: showPhoneDialog }">
      <div class="edit-dialog" @click.stop>
        <div class="dialog-header">
          <h3>修改手机号</h3>
          <button type="button" class="close-btn" @click="closePhoneDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="savePhoneChange">
            <div class="form-group">
              <label class="form-label">新手机号</label>
              <input 
                type="tel" 
                v-model="phoneForm.newPhone" 
                class="verification-form-input" 
                placeholder="请输入新手机号"
                pattern="^1[3-9]\d{9}$"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">验证码</label>
              <div class="verification-input-group">
                <input 
                  type="text" 
                  v-model="phoneForm.verificationCode" 
                  class="form-input" 
                  placeholder="请输入验证码"
                  maxlength="6"
                  required
                />
                <button 
                  type="button" 
                  class="send-code-btn" 
                  @click="sendPhoneVerificationCode"
                  :disabled="phoneCodeCountdown > 0"
                >
                  {{ phoneCodeCountdown > 0 ? `${phoneCodeCountdown}s后重发` : '发送验证码' }}
                </button>
              </div>
            </div>
            <div class="dialog-actions">
              <button type="button" class="btn-secondary" @click="closePhoneDialog">取消</button>
              <button type="submit" class="btn-primary">确认修改</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 修改邮箱弹窗 -->
    <div class="edit-dialog-overlay" :class="{ show: showEmailDialog }">
      <div class="edit-dialog" @click.stop>
        <div class="dialog-header">
          <h3>修改邮箱</h3>
          <button type="button" class="close-btn" @click="closeEmailDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="saveEmailChange">
            <div class="form-group">
              <label class="form-label">新邮箱</label>
              <input 
                type="email" 
                v-model="emailForm.newEmail" 
                class="verification-form-input" 
                placeholder="请输入新邮箱地址"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">验证码</label>
              <div class="verification-input-group">
                <input 
                  type="text" 
                  v-model="emailForm.verificationCode" 
                  class="form-input" 
                  placeholder="请输入验证码"
                  maxlength="6"
                  required
                />
                <button 
                  type="button" 
                  class="send-code-btn" 
                  @click="sendEmailVerificationCode"
                  :disabled="emailCodeCountdown > 0"
                >
                  {{ emailCodeCountdown > 0 ? `${emailCodeCountdown}s后重发` : '发送验证码' }}
                </button>
              </div>
            </div>
            <div class="dialog-actions">
              <button type="button" class="btn-secondary" @click="closeEmailDialog">取消</button>
              <button type="submit" class="btn-primary">确认修改</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 修改个人信息弹窗 -->
    <div class="edit-dialog-overlay" :class="{ show: showInfoDialog }">
      <div class="edit-dialog" @click.stop>
        <div class="dialog-header">
          <h3>修改个人信息</h3>
          <button type="button" class="close-btn" @click="closeInfoDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="saveInfoChange">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">姓名</label>
                <input 
                  type="text" 
                  v-model="infoForm.name" 
                  class="form-input" 
                  placeholder="请输入姓名"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">求职状态</label>
                <SelectBox
                  :key="'jobStatus-' + infoForm.jobStatus"
                  v-model="infoForm.jobStatus"
                  :options="jobStatusOptions"
                  placeholder="请选择求职状态"
                  custom-class="form-select"
                  @change="handleSelectBoxChange('jobStatus', $event)"
                  required
                />
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">性别</label>
                <SelectBox
                  :key="'gender-' + infoForm.gender"
                  v-model="infoForm.gender"
                  :options="genderOptions"
                  placeholder="请选择性别"
                  custom-class="form-select"
                  @change="handleSelectBoxChange('gender', $event)"
                  required
                />
              </div>
            </div>
            <div class="dialog-actions">
              <button type="button" class="btn-secondary" @click="closeInfoDialog">取消</button>
              <button type="submit" class="btn-primary">保存修改</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <div class="edit-dialog-overlay" :class="{ show: showPasswordDialog }">
      <div class="edit-dialog" @click.stop>
        <div class="dialog-header">
          <h3>修改密码</h3>
          <button type="button" class="close-btn" @click="closePasswordDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="savePasswordChange">
            <div class="form-group">
              <label class="form-label">原密码</label>
              <input 
                type="password" 
                v-model="passwordForm.currentPassword" 
                class="verification-form-input" 
                placeholder="请输入原密码"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">新密码</label>
              <input 
                type="password" 
                v-model="passwordForm.newPassword" 
                class="verification-form-input" 
                placeholder="请输入新密码"
                minlength="6"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">确认密码</label>
              <input 
                type="password" 
                v-model="passwordForm.confirmPassword" 
                class="verification-form-input" 
                placeholder="请再次输入新密码"
                required
              />
            </div>
            <div v-if="passwordError" class="error-message">{{ passwordError }}</div>
            <div class="dialog-actions">
              <button type="button" class="btn-secondary" @click="closePasswordDialog">取消</button>
              <button type="submit" class="btn-primary">确认修改</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 通知弹窗 -->
    <div class="popup-overlay" :class="{ show: showNotification }">
      <div class="popup-dialog" @click.stop>
        <div class="popup-icon" :class="notificationType"></div>
        <h3 class="popup-title">{{ notificationTitle }}</h3>
        <p class="popup-message">{{ notificationMessage }}</p>
        <button class="popup-btn" :class="notificationType" @click="closeNotification">知道了</button>
      </div>
    </div>

    <!-- 退出登录确认对话框 -->
    <div class="popup-overlay" :class="{ show: showLogoutConfirm }">
      <div class="popup-dialog confirm-dialog" @click.stop>
        <div class="popup-icon warning"></div>
        <h3 class="popup-title">退出登录</h3>
        <p class="popup-message">确定要退出登录吗？</p>
        <div class="confirm-actions">
          <button class="popup-btn secondary" @click="cancelLogout">取消</button>
          <button class="popup-btn warning" @click="confirmLogout">确定</button>
        </div>
      </div>
    </div>

    <!-- 路由切换确认对话框 -->
    <div class="popup-overlay" :class="{ show: showRouteConfirm }">
      <div class="popup-dialog confirm-dialog" @click.stop>
        <div class="popup-icon warning"></div>
        <h3 class="popup-title">离开页面确认</h3>
        <p class="popup-message">您正在编辑简历信息，离开页面将丢失未保存的修改。确定要离开吗？</p>
        <div class="confirm-actions">
          <button class="popup-btn secondary" @click="cancelRouteChange">继续编辑</button>
          <button class="popup-btn warning" @click="confirmRouteChange">确定离开</button>
        </div>
      </div>
    </div>

    <!-- Toast 提示组件 -->
    <div class="toast-container" :class="{ show: toastConfig.show }">
      <div class="toast" :class="toastConfig.type">
        <div class="toast-icon">
          <img v-if="toastConfig.type === 'success'" src="/icons/chenggong.png" alt="成功" />
          <img v-else-if="toastConfig.type === 'error'" src="/icons/cuowu.png" alt="失败" />
          <img v-else-if="toastConfig.type === 'warning'" src="/icons/zhuyi.png" alt="警告" />
          <img v-else src="/icons/xinxi.png" alt="信息" />
        </div>
        <div class="toast-content">
          <div class="toast-title">{{ toastConfig.title }}</div>
          <div class="toast-message">{{ toastConfig.message }}</div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { ToastMixin, TimerMixin, ModalMixin, StorageMixin, FormValidationMixin } from '../../mixins/mixins.js'
import SelectBox from '../../communal/selectBox.vue'
import { HomeNestedComponent } from './homeNested.js'

export default {
  ...HomeNestedComponent,
  components: {
    SelectBox
  },
  mixins: [ToastMixin, TimerMixin, ModalMixin, StorageMixin, FormValidationMixin],
  data() {
    const baseData = HomeNestedComponent.data();
    return {
      ...baseData,
      degreeOptions: [
        { value: '博士', label: '博士' },
        { value: '硕士', label: '硕士' },
        { value: '本科', label: '本科' },
        { value: '专科', label: '专科' },
        { value: '高中', label: '高中' }
      ],
      genderOptions: [
        { value: 'male', label: '男' },
        { value: 'female', label: '女' }
      ],
      jobStatusOptions: [
        { value: 'ready-graduate', label: '离校-随时到岗' },
        { value: 'current-student-month', label: '在校-月内到岗' },
        { value: 'current-student-opportunity', label: '在校-考虑机会' },
        { value: 'not-looking', label: '不求职' }
      ]
    }
  },
  computed: {
    ...HomeNestedComponent.computed,
    flatIndustryOptions() {
      const options = [];
      this.industryCategories?.forEach(category => {
        category.industries?.forEach(industry => {
          options.push({
            value: industry.name,
            label: industry.name
          });
        });
      });
      return options;
    }
  },
  methods: {
    ...HomeNestedComponent.methods,
    // SelectBox 数据更新处理
    handleSelectBoxChange(fieldName, value) {
      // Vue 3 中直接赋值即可，响应式系统会自动处理
      this.infoForm[fieldName] = value;
    },
    
    // 测试 Toast 功能
    testToast() {
      this.showSuccessToast('测试成功', '这是一个测试消息');
    }
  }
}
</script>

<style scoped>
@import 'homeNested.scss';
@import '../../communal/popupPoge.scss';
@import '../../communal/popupSmooth.scss';
</style>
