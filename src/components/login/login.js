/*
  Designed by: <PERSON><PERSON> Boy
  Original image: https://dribbble.com/shots/5311359-<PERSON><PERSON><PERSON>-Login
*/

// 倒计时混入 - 从mixins.js迁移过来
export const CountdownMixin = {
  data() {
    return {
      countdown: 0,
      countdownTimer: null
    };
  },
  beforeUnmount() {
    this.clearCountdown();
  },
  methods: {
    // 开始倒计时
    startCountdown(seconds = 60) {
      this.clearCountdown();
      this.countdown = seconds;
      
      this.countdownTimer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          this.clearCountdown();
        }
      }, 1000);
    },

    // 清除倒计时
    clearCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      this.countdown = 0;
    }
  }
};

let getButtons = (e) => e.preventDefault()

let changeForm = (e) => {
  console.log('changeForm函数被调用');
  
  // 使用requestAnimationFrame确保DOM完全渲染
  requestAnimationFrame(() => {
    let switchCtn = document.querySelector("#switch-cnt");
    let switchC1 = document.querySelector("#switch-c1");
    let switchC2 = document.querySelector("#switch-c2");
    let aContainer = document.querySelector("#a-container");
    let bContainer = document.querySelector("#b-container");

    console.log('DOM元素查找结果:', {
      switchCtn: !!switchCtn,
      switchC1: !!switchC1,
      switchC2: !!switchC2,
      aContainer: !!aContainer,
      bContainer: !!bContainer
    });

    // 添加空值检查
    if (!switchCtn || !switchC1 || !switchC2 || !aContainer || !bContainer) {
      console.warn('某些DOM元素未找到，请检查页面是否正确加载');
      console.log('详细信息:', {
        switchCtn: switchCtn,
        switchC1: switchC1,
        switchC2: switchC2,
        aContainer: aContainer,
        bContainer: bContainer
      });
      return;
    }

    // 确保元素有正确的初始状态
    if (!switchCtn.classList.contains("is-gx")) {
      // 添加动画效果
      switchCtn.classList.add("is-gx");
      setTimeout(function(){
        switchCtn.classList.remove("is-gx");
      }, 1500);
    }

    // 根据参考代码的切换方式
    switchCtn.classList.toggle("is-txr");
    switchC1.classList.toggle("is-hidden");
    switchC2.classList.toggle("is-hidden");
    aContainer.classList.toggle("is-txl");
    bContainer.classList.toggle("is-txl");
    bContainer.classList.toggle("is-z200");
    
    console.log('切换完成，当前状态:', {
      switchCtnHasTxr: switchCtn.classList.contains("is-txr"),
      switchC1Hidden: switchC1.classList.contains("is-hidden"),
      switchC2Hidden: switchC2.classList.contains("is-hidden"),
      aContainerHasTxl: aContainer.classList.contains("is-txl"),
      bContainerHasTxl: bContainer.classList.contains("is-txl"),
      bContainerHasZ200: bContainer.classList.contains("is-z200")
    });
  });
}

let mainF = (e) => {
  let allButtons = document.querySelectorAll(".submit");
  // 注意：不再为.switch-btn绑定事件监听器，因为Vue.js通过@click处理
  
  // 添加空值检查
  if (allButtons.length === 0) {
    console.warn('未找到提交按钮');
  }
  
  for (var i = 0; i < allButtons.length; i++)
    allButtons[i].addEventListener("click", getButtons );
}

// 忘记密码相关功能
let forgotPasswordDialog = null;

let showError = (message) => {
  const errorElement = document.querySelector('.error-message');
  if (errorElement) {
    errorElement.textContent = message;
    errorElement.style.color = '#e74c3c';
  }
}

let showSuccess = (message) => {
  const errorElement = document.querySelector('.error-message');
  if (errorElement) {
    errorElement.textContent = message;
    errorElement.style.color = '#27ae60';
  }
}

let clearMessage = () => {
  const errorElement = document.querySelector('.error-message');
  if (errorElement) {
    errorElement.textContent = '';
  }
}

let showForgotPasswordDialog = () => {
  forgotPasswordDialog = document.querySelector(".forgot-password-overlay");
  if (forgotPasswordDialog) {
    forgotPasswordDialog.style.display = "flex";
    setTimeout(() => {
      forgotPasswordDialog.classList.add("show");
    }, 10);
  } else {
    console.warn('未找到忘记密码对话框元素');
  }
}

let hideForgotPasswordDialog = () => {
  if (forgotPasswordDialog) {
    forgotPasswordDialog.classList.remove("show");
    setTimeout(() => {
      forgotPasswordDialog.style.display = "none";
    }, 300);
  }
}

let sendVerificationCode = (contact) => {
  if (!contact) {
    showError('请输入手机号或邮箱');
    return false;
  }
  
  // 这里可以添加发送验证码的API调用
  console.log('发送验证码到:', contact);
  showSuccess('验证码已发送，请查收');
  
  return true;
}

let resetPassword = (formData) => {
  const { contact, verificationCode, newPassword, confirmPassword } = formData;
  
  if (!contact || !verificationCode || !newPassword || !confirmPassword) {
    showError('请填写完整信息');
    return false;
  }
  
  if (newPassword !== confirmPassword) {
    showError('两次输入的密码不一致');
    return false;
  }
  
  // 这里可以添加重置密码的API调用
  console.log('重置密码:', { contact, verificationCode, newPassword });
  showSuccess('密码重置成功');
  setTimeout(() => {
    hideForgotPasswordDialog();
  }, 1500);
  
  return true;
}

// 确保在DOM加载后立即执行初始化
if (typeof window !== 'undefined') {
  window.initLoginPage = function() {
    console.log('initLoginPage被调用');
    mainF();
    console.log('mainF执行完成');
  };

  // 导出changeForm函数到window对象
  window.changeForm = changeForm;
  console.log('changeForm函数已导出到window对象');

  // 导出忘记密码相关函数
  window.forgotPassword = {
    showDialog: showForgotPasswordDialog,
    hideDialog: hideForgotPasswordDialog,
    sendCode: sendVerificationCode,
    resetPassword: resetPassword,
    clearMessage: clearMessage
  };
  
  console.log('login.js加载完成，函数已绑定到window对象');
} 