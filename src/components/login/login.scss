@use 'sass:color';

// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$gray: #a0a5a8;
$black: #181818;
$purple: #4B70E2;
$text-secondary: #64748b;
$transition: 0.3s;
$transition-slow: 1.25s;

*, *::after, *::before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 只对特定元素禁用文本选择 */
.main, .container, .switch {
  user-select: none;
}

/* 确保可交互元素可以被点击 */
button, input, select, .toggle-option {
  user-select: none;
  pointer-events: auto;
}

/* 确保不出现滚动条 */
html {
  overflow: hidden;
}

body {
  overflow: hidden;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  background-color: $neu-1;
  color: $gray;
  overflow: hidden;
  position: relative;
}

#app {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: $neu-1;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.main {
  position: relative;
  width: 1000px;
  max-width: 100%;
  height: 600px;
  max-height: 100%;
  padding: 20px;
  background-color: $neu-1;
  border-radius: 0;
  overflow: hidden;
  z-index: 1000;
  box-sizing: border-box;
  margin: 0;
  
  &[id="app"] {
    -webkit-app-region: drag;
    cursor: default;
    
    /* 确保可交互元素不受拖拽影响 */
    button, input, select, .toggle-option, .form__link {
      -webkit-app-region: no-drag;
      cursor: pointer;
    }
    
    input {
      cursor: text;
    }
  }
}

.close-app-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  font-size: 24px;
  color: $gray;
  cursor: pointer;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: color 0.3s ease;
  -webkit-app-region: no-drag;
  
  &:hover {
    color: $black;
  }
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  width: 600px;
  max-width: 60%;
  height: 100%;
  padding: 25px;
  background-color: $neu-1;
  transition: $transition-slow;
  overflow: hidden;
  box-sizing: border-box;
}

.form {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  
  &__input {
    width: 350px;
    max-width: 90%;
    height: 40px;
    margin: 4px 0;
    padding-left: 25px;
    font-size: 13px;
    letter-spacing: .15px;
    border: none;
    outline: none;
    font-family: 'Montserrat', sans-serif;
    background-color: $white;
    transition: .25s ease;
    border-radius: 8px;
    box-shadow:
      inset 2px 2px 4px $neu-2,
      inset -2px -2px 4px $white;
    box-sizing: border-box;

    &:focus {
      box-shadow:
        inset 4px 4px 4px $neu-2,
        inset -4px -4px 4px $white;
    }
  }
  
  &__span {
    margin-top: 30px;
    margin-bottom: 12px;
  }
  
  &__link {
    color: $black;
    font-size: 15px;
    margin-top: 25px;
    border-bottom: 1px solid $gray;
    line-height: 2;
  }
}

.verification-group {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 350px;
  max-width: 90%;
  margin: 4px 0;
  box-sizing: border-box;
}

.verification-input {
  flex: 1;
  width: auto;
}

.verification-btn {
  width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.5px;
  background-color: $purple;
  color: $white;
  box-shadow:
    4px 4px 8px $neu-2,
    -4px -4px 8px $white;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all 0.25s ease;
  
  &:hover {
    box-shadow:
      6px 6px 10px $neu-2,
      -6px -6px 10px $white;
    transform: scale(.985);
  }
  
  &:active {
    box-shadow:
      2px 2px 6px $neu-2,
      -2px -2px 6px $white;
    transform: scale(.97);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 注册方式选择器样式
.registration-type-selector {
  display: flex;
  justify-content: center;
  margin: 25px 0;
  
  .toggle-container {
    position: relative;
    width: 280px;
    height: 50px;
    
    .toggle-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: $neu-1;
      border-radius: 25px;
      box-shadow:
        inset 3px 3px 6px $neu-2,
        inset -3px -3px 6px $white;
      
      .toggle-slider {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 136px;
        height: 42px;
        background: linear-gradient(145deg, color.adjust($purple, $lightness: 10%), $purple);
        border-radius: 21px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow:
          2px 2px 6px rgba($neu-2, 0.8),
          -2px -2px 6px rgba($white, 0.8);
        
        &.active {
          transform: translateX(136px);
        }
      }
    }
    
    .toggle-options {
      position: relative;
      display: flex;
      width: 100%;
      height: 100%;
      z-index: 2;
      
      .toggle-option {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
        border-radius: 25px;
        
        .option-text {
          font-size: 13px;
          font-weight: 600;
          letter-spacing: 0.5px;
          transition: all 0.3s ease;
          color: $gray;
        }
        
        &.active {
          .option-text {
            color: $white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            transform: scale(1.05);
          }
        }
        
        &:hover:not(.active) {
          .option-text {
            color: color.adjust($gray, $lightness: -10%);
            transform: scale(1.02);
          }
        }
      }
    }
  }
}

// 手机号输入组样式
.phone-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 350px;
  max-width: 90%;
  margin: 4px 0;
  
  .region-selector {
    width: 110px;
    height: 40px;
    padding: 0 12px;
    font-size: 13px;
    font-weight: 500;
    border: none;
    outline: none;
    font-family: 'Montserrat', sans-serif;
      background-color: $white;
    transition: all .3s ease;
    border-radius: 10px;
    box-shadow:
      inset 2px 2px 4px $neu-2,
      inset -2px -2px 4px $white;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    color: $black;
    
    &:focus {
      box-shadow:
        inset 4px 4px 6px $neu-2,
        inset -4px -4px 6px $white;
      transform: scale(0.98);
    }
    
    &:hover {
      box-shadow:
        inset 3px 3px 5px $neu-2,
        inset -3px -3px 5px $white;
    }
    
    option {
        background-color: $white;
      color: $black;
      padding: 8px;
      font-size: 13px;
    }
  }
  
  .phone-input {
    flex: 1;
    width: auto;
    margin: 0;
    border-radius: 10px;
    
    &:focus {
      box-shadow:
        inset 4px 4px 6px $neu-2,
        inset -4px -4px 6px $white;
      transform: scale(0.98);
    }
  }
}

// 注册区域样式
.phone-registration,
.email-registration {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 34px;
  font-weight: 700;
  line-height: 3;
  color: $black;
}

.description {
  font-size: 14px;
  letter-spacing: .25px;
  text-align: center;
  line-height: 1.6;
}

.button {
  width: 180px;
  height: 50px;
  border-radius: 25px;
  margin-top: 50px;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 1.15px;
  background-color: $purple;
  color: $white;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  border: none;
  outline: none;
}

.a-container {
  z-index: 100;
  left: calc(100% - 600px);
  transition: $transition-slow;
}

.b-container {
  left: calc(100% - 600px);
  z-index: 0;
  transition: $transition-slow;
}

.switch {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 400px;
  max-width: 40%;
  padding: 50px;
  z-index: 200;
  transition: $transition-slow;
  background-color: $neu-1;
  overflow: hidden;
  border-radius: 16px;
  box-shadow:
    4px 4px 10px $neu-2,
    -4px -4px 10px $white;
  box-sizing: border-box;

  &__container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: absolute;
    width: 400px;
    max-width: 100%;
    padding: 50px 55px;
    transition: $transition-slow;
    box-sizing: border-box;
    overflow: hidden;
  }

  &__button {
    cursor: pointer;
    
    &:hover {
      box-shadow:
        6px 6px 10px $neu-2,
        -6px -6px 10px $white;
      transform: scale(.985);
      transition: .25s;
    }
    
    &:active,
    &:focus {
      box-shadow:
        2px 2px 6px $neu-2,
        -2px -2px 6px $white;
      transform: scale(.97);
      transition: .25s;
    }
  }
}

.is-txr {
  left: calc(100% - 390px);
  transition: $transition-slow;
  transform-origin: left;
}

.is-txl {
  left: 0;
  transition: $transition-slow;
  transform-origin: right;
}

.is-z200 {
  z-index: 200;
  transition: $transition-slow;
}

.is-hidden {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  transition: $transition-slow;
}

.is-gx {
  animation: is-gx $transition;
}

@keyframes is-gx {
  0%, 10%, 100% { 
    width: 400px; 
  }
  30%, 50% { 
    width: 500px;
  }
}

.forgot-password-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

.forgot-password-overlay .popup-dialog {
  // 使用全局 .popup-dialog 的基础上定制尺寸与内容布局
  border-radius: 16px;
  padding: 30px;
  width: 400px;
  max-width: 90%;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 24px;
      font-weight: 700;
      color: $black;
      margin: 0;
    }
    
    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      color: $gray;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all $transition ease;
      
      &:hover {
        color: $black;
        background-color: rgba($neu-2, 0.2);
      }
    }
  }
  
  .dialog-content {
    p {
      margin-bottom: 20px;
      color: $gray;
      text-align: center;
    }
    
    .form__input {
      margin-bottom: 15px;
    }
    
    .form__button {
      width: 100%;
      margin-top: 20px;
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    .verification-group {
      width: 100%;
      margin-bottom: 15px;
    }
     
    .verification-input {
      margin-bottom: 0;
    }
    
    .verification-btn {
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    .error-message {
      color: #e74c3c;
      font-size: 12px;
      margin-top: 5px;
      text-align: center;
      min-height: 15px;
    }
  }
}