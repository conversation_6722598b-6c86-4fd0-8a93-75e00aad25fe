<template>
  <div class="main" id="app">
    <!-- 关闭应用按钮 -->
    <button class="close-app-btn" @click="handleCloseApp" @mousedown="handleCloseApp">&times;</button>
    <div class="container a-container" id="a-container">
      <form id="a-form" class="form" method="" action="">
        <h2 class="form_title title">登录OfferHub</h2>
        <span class="form__span">使用您的手机号或邮箱登录</span>
        <input class="form__input" type="text" placeholder="手机号或邮箱" />
        <input class="form__input" type="password" placeholder="密码" />
        <a class="form__link" @click="handleForgotPassword">忘记密码？</a>
        <button @click="handleLogin" class="form__button button submit">登录</button>
      </form>
    </div>
    
    <div class="container b-container" id="b-container">
      <form id="b-form" class="form" method="" action="">
        <h2 class="form_title title">注册OfferHub</h2>
        
        <!-- 注册方式选择器 -->
        <div class="registration-type-selector">
          <div class="toggle-container">
            <div class="toggle-background">
              <div class="toggle-slider" :class="{ 'active': registrationType === 'email' }"></div>
            </div>
            <div class="toggle-options">
              <label class="toggle-option" :class="{ 'active': registrationType === 'phone' }" @click="handlePhoneToggleClick">
                <span class="option-text">手机号注册</span>
              </label>
              <label class="toggle-option" :class="{ 'active': registrationType === 'email' }" @click="handleEmailToggleClick">
                <span class="option-text">邮箱注册</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 手机号注册 -->
        <div v-if="registrationType === 'phone'" class="phone-registration">
                  <div class="phone-input-group">
          <SelectBox
            v-model="selectedRegion"
            :options="regionOptions"
            placeholder="选择地区"
            custom-class="region-selector"
            size="sm"
          />
            <input class="form__input phone-input" type="tel" placeholder="手机号" v-model="phoneNumber" />
          </div>
          <div class="verification-group">
            <input class="form__input verification-input" type="text" placeholder="验证码" v-model="verificationCode" />
            <button class="verification-btn send-code-btn" @click="handleSendSignUpCode">获取验证码</button>
          </div>
        </div>

        <!-- 邮箱注册 -->
        <div v-if="registrationType === 'email'" class="email-registration">
          <input class="form__input" type="email" placeholder="邮箱地址" v-model="emailAddress" />
          <div class="verification-group">
            <input class="form__input verification-input" type="text" placeholder="验证码" v-model="verificationCode" />
            <button class="verification-btn send-code-btn" @click="handleSendEmailCode">获取验证码</button>
          </div>
        </div>

        <input class="form__input" type="password" placeholder="密码" v-model="password" />
        <input class="form__input" type="password" placeholder="确认密码" v-model="confirmPassword" />
        <button @click="handleSignUp" class="form__button button submit">注册</button>
      </form>
    </div>
    
    <div class="switch" id="switch-cnt">
      <div class="switch__container" id="switch-c1">
        <h2 class="switch__title title">欢迎回来</h2>
        <p class="switch__description description">如果您没有账号请点击下方按钮进行注册</p>
        <button class="switch__button button switch-btn" @click="handleSwitchToSignUp">注册</button>
      </div>
      <div class="switch__container is-hidden" id="switch-c2">
        <h2 class="switch__title title">你好，朋友！</h2>
        <p class="switch__description description">如果您有账号请点击下方按钮进行登录</p>
        <button class="switch__button button switch-btn" @click="handleSwitchToLogin">登录</button>
      </div>
    </div>
    
    <!-- 忘记密码对话框（统一使用全局popup样式） -->
    <div class="popup-overlay forgot-password-overlay" style="display: none;" @click="handleCloseDialog">
      <div class="popup-dialog" @click.stop>
        <div class="dialog-header">
          <h3>忘记密码</h3>
          <button class="close-btn" @click="handleCloseDialog" @mousedown="handleCloseDialog">&times;</button>
        </div>
        
        <div class="dialog-content">
          <div class="step-1" v-if="!forgotPasswordForm.contact">
            <p>请输入您的手机号或邮箱，我们将发送验证码</p>
            <input 
              v-model="forgotPasswordContact" 
              class="form__input" 
              type="text" 
              placeholder="手机号或邮箱" 
            />
            <button 
              @click="handleSendVerificationCode" 
              class="form__button button"
              :disabled="countdown > 0"
            >
              {{ countdown > 0 ? `${countdown}秒后重发` : '发送验证码' }}
            </button>
            <div class="error-message"></div>
          </div>
          
          <div class="step-2" v-else>
            <p>请输入验证码和新密码</p>
            <input 
              v-model="forgotPasswordForm.contact" 
              class="form__input" 
              type="text" 
              placeholder="手机号或邮箱" 
              readonly
            />
            <div class="verification-group">
              <input 
                v-model="forgotPasswordForm.verificationCode" 
                class="form__input verification-input" 
                type="text" 
                placeholder="验证码" 
              />
              <button 
                @click="handleSendVerificationCode" 
                class="verification-btn send-code-btn"
                :disabled="countdown > 0"
              >
                {{ countdown > 0 ? `${countdown}s` : '重发' }}
              </button>
            </div>
            <input 
              v-model="forgotPasswordForm.newPassword" 
              class="form__input" 
              type="password" 
              placeholder="新密码" 
            />
            <input 
              v-model="forgotPasswordForm.confirmPassword" 
              class="form__input" 
              type="password" 
              placeholder="确认新密码" 
            />
            <button @click="handleResetPassword" class="form__button button">重置密码</button>
            <div class="error-message"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { WindowControlMixin, DraggableMixin, FormValidationMixin, ToastMixin, TimerMixin, StorageMixin } from '../../mixins/mixins.js'
import { CountdownMixin } from './login.js' // 导入登录页面的JavaScript逻辑和倒计时混入
import SelectBox from '../../communal/selectBox.vue'

export default {
  name: 'Login',
  components: {
    SelectBox
  },
  mixins: [WindowControlMixin, DraggableMixin, CountdownMixin, FormValidationMixin, ToastMixin, TimerMixin, StorageMixin],
  data() {
    return {
      // 注册表单数据
      registrationType: 'phone', // 默认手机号注册
      selectedRegion: '+86', // 默认中国大陆
      regionOptions: [
        { value: '+86', label: '中国 +86' },
        { value: '+1', label: '美国 +1' },
        { value: '+44', label: '英国 +44' },
        { value: '+81', label: '日本 +81' },
        { value: '+82', label: '韩国 +82' },
        { value: '+65', label: '新加坡 +65' },
        { value: '+852', label: '香港 +852' },
        { value: '+853', label: '澳门 +853' },
        { value: '+886', label: '台湾 +886' }
      ],
      phoneNumber: '',
      emailAddress: '',
      verificationCode: '',
      password: '',
      confirmPassword: '',
      
      // 忘记密码表单数据
      forgotPasswordContact: '',
      forgotPasswordStep: 1, // 1: 输入联系方式, 2: 输入验证码和新密码
      forgotPasswordForm: {
        contact: '',
        verificationCode: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  },
  mounted() {
    // 环境检测和调试信息
    console.log('Login组件挂载完成', {
      isElectron: window.electronAPI ? true : false,
      userAgent: navigator.userAgent,
      windowFeatures: {
        electronAPI: !!window.electronAPI,
        initLoginPage: !!window.initLoginPage,
        changeForm: !!window.changeForm
      }
    });
    
    // 确保DOM完全渲染后再初始化
    this.$nextTick(() => {
      console.log('准备初始化登录页面', {
        initLoginPageAvailable: !!window.initLoginPage,
        changeFormAvailable: !!window.changeForm
      });
      
      if (window.initLoginPage) {
        try {
          window.initLoginPage();
          console.log('登录页面初始化完成');
        } catch (error) {
          console.error('登录页面初始化出错:', error);
        }
      } else {
        console.warn('initLoginPage函数未找到，可能需要手动初始化');
      }
      
      // 再次检查函数是否可用
      console.log('初始化后函数状态', {
        initLoginPageAvailable: !!window.initLoginPage,
        changeFormAvailable: !!window.changeForm
      });
      
      // 如果函数仍然不可用，提供额外的调试信息
      if (!window.changeForm) {
        console.log('window对象上的可用函数:', Object.keys(window).filter(key => key.includes('change') || key.includes('login')));
      }
    });
  },
  methods: {
    // 处理邮箱注册切换点击
    handleEmailToggleClick(e) {
      console.log('邮箱注册切换被点击', {
        event: e,
        currentType: this.registrationType,
        isElectron: window.electronAPI ? true : false,
        userAgent: navigator.userAgent
      });
      this.registrationType = 'email';
    },
    
    // 处理手机号注册切换点击
    handlePhoneToggleClick(e) {
      console.log('手机号注册切换被点击', {
        event: e,
        currentType: this.registrationType,
        isElectron: window.electronAPI ? true : false
      });
      this.registrationType = 'phone';
    },
    
    handleSignUp(e) {
      e.preventDefault();
      console.log('注册按钮被点击', {
        registrationType: this.registrationType,
        phoneNumber: this.phoneNumber,
        emailAddress: this.emailAddress,
        isElectron: window.electronAPI ? true : false
      });
    },
    handleSendSignUpCode(e) {
      e.preventDefault();
      console.log('发送手机注册验证码');
      // 这里可以添加发送手机注册验证码的逻辑
      const fullPhoneNumber = this.selectedRegion + this.phoneNumber;
      console.log('发送验证码到:', fullPhoneNumber);
    },
    handleSendEmailCode(e) {
      e.preventDefault();
      console.log('发送邮箱注册验证码', {
        event: e,
        emailAddress: this.emailAddress,
        isElectron: window.electronAPI ? true : false,
        target: e.target,
        currentTarget: e.currentTarget
      });
      // 这里可以添加发送邮箱注册验证码的逻辑
      console.log('发送验证码到:', this.emailAddress);
    },
    handleLogin(e) {
      e.preventDefault();
      console.log('登录按钮被点击');
      
      // 这里应该有真实的登录验证逻辑
      // 模拟登录成功
      
      try {
        // 检查是否在Electron环境中
        if (window.electronAPI) {
          // 同时进行：页面跳转和窗口大小调整
          this.$router.push('/option/interview');
          window.electronAPI.loginSuccess();
          console.log('页面跳转和窗口调整同时进行');
        } else {
          // Web环境，直接跳转
          console.log('Web环境，直接跳转');
          this.$router.push('/option/interview');
        }
        
      } catch (error) {
        console.error('发送登录成功消息失败:', error);
        // 如果Electron API不可用，直接跳转
        this.$router.push('/option/interview');
      }
    },
    handleSwitchToLogin() {
      console.log('handleSwitchToLogin被调用', {
        changeFormAvailable: !!window.changeForm,
        initLoginPageAvailable: !!window.initLoginPage
      });
      try {
        // 确保在下一个tick中执行，保证DOM已渲染
        this.$nextTick(() => {
          if (window.changeForm) {
            console.log('调用changeForm函数');
            window.changeForm();
          } else {
            console.warn('changeForm函数未找到，使用备用切换方法');
            this.fallbackChangeForm();
          }
        });
      } catch (error) {
        console.error('切换表单时出错:', error);
        // 如果出错，使用备用方法
        this.fallbackChangeForm();
      }
    },
    handleSwitchToSignUp() {
      console.log('handleSwitchToSignUp被调用', {
        changeFormAvailable: !!window.changeForm,
        initLoginPageAvailable: !!window.initLoginPage
      });
      try {
        // 确保在下一个tick中执行，保证DOM已渲染
        this.$nextTick(() => {
          if (window.changeForm) {
            console.log('调用changeForm函数');
            window.changeForm();
          } else {
            console.warn('changeForm函数未找到，使用备用切换方法');
            this.fallbackChangeForm();
          }
        });
      } catch (error) {
        console.error('切换表单时出错:', error);
        // 如果出错，使用备用方法
        this.fallbackChangeForm();
      }
    },
    // 备用的表单切换方法
    fallbackChangeForm() {
      console.log('使用备用切换方法');
      
      this.$nextTick(() => {
        const switchCtn = document.querySelector("#switch-cnt");
        const switchC1 = document.querySelector("#switch-c1");
        const switchC2 = document.querySelector("#switch-c2");
        const aContainer = document.querySelector("#a-container");
        const bContainer = document.querySelector("#b-container");

        console.log('备用方法DOM元素查找结果:', {
          switchCtn: !!switchCtn,
          switchC1: !!switchC1,
          switchC2: !!switchC2,
          aContainer: !!aContainer,
          bContainer: !!bContainer
        });

        if (!switchCtn || !switchC1 || !switchC2 || !aContainer || !bContainer) {
          console.warn('备用方法：某些DOM元素未找到');
          return;
        }

        // 添加动画效果
        switchCtn.classList.add("is-gx");
        setTimeout(() => {
          switchCtn.classList.remove("is-gx");
        }, 1500);

        // 执行切换
        switchCtn.classList.toggle("is-txr");
        switchC1.classList.toggle("is-hidden");
        switchC2.classList.toggle("is-hidden");
        aContainer.classList.toggle("is-txl");
        bContainer.classList.toggle("is-txl");
        bContainer.classList.toggle("is-z200");
        
        console.log('备用方法切换完成');
      });
    },
    handleForgotPassword(e) {
      e.preventDefault();
      if (window.forgotPassword && window.forgotPassword.showDialog) {
        window.forgotPassword.showDialog();
        if (window.forgotPassword && window.forgotPassword.clearMessage) {
          window.forgotPassword.clearMessage();
        }
      }
    },
    handleSendVerificationCode() {
      const contact = this.forgotPasswordForm.contact || this.forgotPasswordContact;
      
      // 验证联系方式
      if (!contact) {
        this.showErrorMessage(document.querySelector('.error-message'), '请输入手机号或邮箱');
        return;
      }
      
      if (!this.validatePhone(contact) && !this.validateEmail(contact)) {
        this.showErrorMessage(document.querySelector('.error-message'), '请输入正确的手机号或邮箱');
        return;
      }
      
      // 发送验证码
      if (window.forgotPassword && window.forgotPassword.sendCode) {
        if (window.forgotPassword.sendCode(contact)) {
          this.forgotPasswordForm.contact = contact;
          this.startCountdown(60); // 使用混入的倒计时方法
          this.showSuccessMessage(document.querySelector('.error-message'), '验证码已发送');
        }
      }
    },
    handleResetPassword() {
      const formData = this.forgotPasswordForm;
      if (window.forgotPassword && window.forgotPassword.resetPassword) {
        if (window.forgotPassword.resetPassword(formData)) {
          this.resetForgotPasswordForm();
          this.handleCloseDialog();
        }
      }
    },
    handleCloseDialog() {
      if (window.forgotPassword && window.forgotPassword.hideDialog) {
        window.forgotPassword.hideDialog();
      }
    },
    resetForgotPasswordForm() {
      this.forgotPasswordContact = '';
      this.forgotPasswordForm = {
        contact: '',
        verificationCode: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.clearCountdown(); // 使用混入的方法
    },
    
    // 验证手机号注册表单
    validatePhoneRegistration() {
      if (!this.phoneNumber) {
        this.showErrorToast('请输入手机号');
        return false;
      }
      if (!this.validatePhone(this.phoneNumber)) {
        this.showErrorToast('请输入正确的手机号格式');
        return false;
      }
      if (!this.password) {
        this.showErrorToast('请输入密码');
        return false;
      }
      if (!this.validatePassword(this.password)) {
        this.showErrorToast('密码长度至少6位');
        return false;
      }
      if (this.password !== this.confirmPassword) {
        this.showErrorToast('两次密码输入不一致');
        return false;
      }
      if (!this.verificationCode) {
        this.showErrorToast('请输入验证码');
        return false;
      }
      return true;
    },

    // 验证邮箱注册表单
    validateEmailRegistration() {
      if (!this.emailAddress) {
        this.showErrorToast('请输入邮箱地址');
        return false;
      }
      if (!this.validateEmail(this.emailAddress)) {
        this.showErrorToast('请输入正确的邮箱格式');
        return false;
      }
      if (!this.password) {
        this.showErrorToast('请输入密码');
        return false;
      }
      if (!this.validatePassword(this.password)) {
        this.showErrorToast('密码长度至少6位');
        return false;
      }
      if (this.password !== this.confirmPassword) {
        this.showErrorToast('两次密码输入不一致');
        return false;
      }
      if (!this.verificationCode) {
        this.showErrorToast('请输入验证码');
        return false;
      }
      return true;
    },

    // 验证忘记密码表单
    validateForgotPasswordForm() {
      if (!this.forgotPasswordForm.contact) {
        this.showErrorToast('请输入手机号或邮箱');
        return false;
      }
      
      // 判断是手机号还是邮箱
      const isPhone = /^1[3-9]\d{9}$/.test(this.forgotPasswordForm.contact);
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.forgotPasswordForm.contact);
      
      if (!isPhone && !isEmail) {
        this.showErrorToast('请输入正确的手机号或邮箱格式');
        return false;
      }
      
      if (!this.forgotPasswordForm.verificationCode) {
        this.showErrorToast('请输入验证码');
        return false;
      }
      if (!this.forgotPasswordForm.newPassword) {
        this.showErrorToast('请输入新密码');
        return false;
      }
      if (!this.validatePassword(this.forgotPasswordForm.newPassword)) {
        this.showErrorToast('密码长度至少6位');
        return false;
      }
      if (this.forgotPasswordForm.newPassword !== this.forgotPasswordForm.confirmPassword) {
        this.showErrorToast('两次密码输入不一致');
        return false;
      }
      return true;
    },

    // 发送注册验证码
    sendRegistrationCode() {
      let contact = '';
      if (this.registrationType === 'phone') {
        if (!this.validatePhone(this.phoneNumber)) {
          this.showErrorToast('请输入正确的手机号');
          return;
        }
        contact = this.phoneNumber;
      } else {
        if (!this.validateEmail(this.emailAddress)) {
          this.showErrorToast('请输入正确的邮箱');
          return;
        }
        contact = this.emailAddress;
      }

      // 使用CountdownMixin开始倒计时
      this.startCountdown(60);
      this.showSuccessToast(`验证码已发送至${contact}`);
    },

    // 发送忘记密码验证码
    sendForgotPasswordCode() {
      if (!this.forgotPasswordForm.contact) {
        this.showErrorToast('请输入手机号或邮箱');
        return;
      }

      // 判断联系方式类型并验证
      const isPhone = /^1[3-9]\d{9}$/.test(this.forgotPasswordForm.contact);
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.forgotPasswordForm.contact);
      
      if (!isPhone && !isEmail) {
        this.showErrorToast('请输入正确的手机号或邮箱格式');
        return;
      }

      // 使用CountdownMixin开始倒计时
      this.startCountdown(60);
      this.showSuccessToast(`验证码已发送至${this.forgotPasswordForm.contact}`);
    }
  }
}
</script>

<style scoped>
@import 'login.scss';
</style>
<style>
@import '../../communal/popupPoge.scss';
</style>
