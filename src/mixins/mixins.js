// Vue混入(Mixins)文件
// 统一管理公共的组件逻辑，避免重复代码
// 按功能模块和方法名称排序

// ===========================================
// 拖拽功能混入 (DraggableMixin)
// ===========================================
// 名称: DraggableMixin
// 使用的组件: Login.vue, Option.vue
// 功能: 设置窗口拖拽区域，让用户可以拖拽移动窗口
export const DraggableMixin = {
  mounted() {
    this.setupDraggable();
  },
  methods: {
    setupDraggable() {
      // 设置主容器为可拖动区域
      const appElement = document.getElementById('app');
      if (appElement) {
        appElement.style.webkitAppRegion = 'drag';
      }
      
      // 设置按钮和输入框为不可拖动区域
      const nonDraggableElements = document.querySelectorAll(
        'button, input, a, .control-btn, .menu-link, .form__input, .form__button'
      );
      nonDraggableElements.forEach(element => {
        element.style.webkitAppRegion = 'no-drag';
      });
    }
  }
};

// ===========================================
// 表单验证混入 (FormValidationMixin)
// ===========================================
// 名称: FormValidationMixin
// 使用的组件: Login.vue
// 功能: 提供常用的表单验证方法(手机号、邮箱、密码)和消息显示
export const FormValidationMixin = {
  methods: {
    // 清除消息
    clearMessage(element) {
      if (element) {
        element.textContent = '';
        element.style.display = 'none';
      }
    },

    // 显示错误消息
    showErrorMessage(element, message) {
      if (element) {
        element.textContent = message;
        element.style.color = '#ef4444';
        element.style.display = 'block';
      }
    },

    // 显示成功消息
    showSuccessMessage(element, message) {
      if (element) {
        element.textContent = message;
        element.style.color = '#10b981';
        element.style.display = 'block';
      }
    },

    // 验证邮箱
    validateEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    // 验证密码强度
    validatePassword(password) {
      return password && password.length >= 6;
    },

    // 验证手机号
    validatePhone(phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    }
  }
};

// ===========================================
// 模态框管理混入 (ModalMixin)
// ===========================================
// 名称: ModalMixin
// 使用的组件: Interview.vue, homeNested.vue, money.vue
// 功能: 统一管理模态框的显示、隐藏和状态
export const ModalMixin = {
  data() {
    return {
      modalStates: {}
    };
  },
  methods: {
    // 关闭模态框
    closeModal(modalName) {
      if (this.modalStates[modalName] !== undefined) {
        this.modalStates[modalName] = false;
      } else {
        // 兼容原有属性名
        const modalProp = `show${modalName.charAt(0).toUpperCase() + modalName.slice(1)}`;
        if (this[modalProp] !== undefined) {
          this[modalProp] = false;
        }
      }
    },

    // 打开模态框
    openModal(modalName) {
      if (this.modalStates[modalName] !== undefined) {
        this.modalStates[modalName] = true;
      } else {
        // 兼容原有属性名
        const modalProp = `show${modalName.charAt(0).toUpperCase() + modalName.slice(1)}`;
        if (this[modalProp] !== undefined) {
          this[modalProp] = true;
        }
      }
    },

    // 切换模态框状态
    toggleModal(modalName) {
      if (this.modalStates[modalName] !== undefined) {
        this.modalStates[modalName] = !this.modalStates[modalName];
      } else {
        // 兼容原有属性名
        const modalProp = `show${modalName.charAt(0).toUpperCase() + modalName.slice(1)}`;
        if (this[modalProp] !== undefined) {
          this[modalProp] = !this[modalProp];
        }
      }
    }
  }
};

// ===========================================
// 数据持久化混入 (StorageMixin)
// ===========================================
// 名称: StorageMixin
// 使用的组件: homeNested.vue, money.vue, Interview.vue
// 功能: 提供localStorage的保存、读取、删除操作的统一方法
export const StorageMixin = {
  methods: {
    // 批量加载数据
    loadBatchFromStorage(keys, defaultValues = {}) {
      const result = {};
      keys.forEach(key => {
        result[key] = this.loadFromStorage(key, defaultValues[key] || null);
      });
      return result;
    },

    // 从localStorage读取
    loadFromStorage(key, defaultValue = null) {
      try {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : defaultValue;
      } catch (error) {
        console.error('读取数据失败:', error);
        return defaultValue;
      }
    },

    // 删除localStorage数据
    removeFromStorage(key) {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (error) {
        console.error('删除数据失败:', error);
        return false;
      }
    },

    // 批量保存数据
    saveBatchToStorage(dataMap) {
      const results = {};
      Object.keys(dataMap).forEach(key => {
        results[key] = this.saveToStorage(key, dataMap[key]);
      });
      return results;
    },

    // 保存到localStorage
    saveToStorage(key, value) {
      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch (error) {
        console.error('保存数据失败:', error);
        return false;
      }
    }
  }
};

// ===========================================
// 定时器管理混入 (TimerMixin)
// ===========================================
// 名称: TimerMixin
// 使用的组件: Interview.vue, homeNested.vue, Login.vue
// 功能: 统一管理定时器的创建、清除和状态
export const TimerMixin = {
  data() {
    return {
      timers: {}
    };
  },
  beforeUnmount() {
    this.clearAllTimers();
  },
  methods: {
    // 清除所有定时器
    clearAllTimers() {
      Object.keys(this.timers).forEach(timerName => {
        this.clearTimer(timerName);
      });
    },

    // 清除指定定时器
    clearTimer(timerName) {
      if (this.timers[timerName]) {
        clearInterval(this.timers[timerName]);
        delete this.timers[timerName];
      }
    },

    // 设置定时器
    setTimer(timerName, callback, interval = 1000) {
      // 清除已存在的同名定时器
      this.clearTimer(timerName);
      
      // 创建新定时器
      this.timers[timerName] = setInterval(callback, interval);
      return this.timers[timerName];
    }
  }
};

// ===========================================
// 通知消息混入 (ToastMixin)
// ===========================================
// 名称: ToastMixin
// 使用的组件: Interview.vue, homeNested.vue, money.vue
// 功能: 统一的消息提示功能，支持不同类型的通知
export const ToastMixin = {
  data() {
    return {
      toastConfig: {
        show: false,
        message: '',
        type: 'success', // success, error, warning, info
        duration: 3000,
        title: ''
      }
    };
  },
  methods: {
    // 隐藏提示
    hideToast() {
      this.toastConfig.show = false;
    },

    // 显示错误提示
    showErrorToast(message, title = '错误') {
      this.showToast(message, title, 'error');
    },

    // 显示信息提示
    showInfoToast(message, title = '提示') {
      this.showToast(message, title, 'info');
    },

    // 显示成功提示
    showSuccessToast(message, title = '成功') {
      this.showToast(message, title, 'success');
    },

    // 显示提示消息
    showToast(message, title = '提示', type = 'success', duration = 3000) {
      this.toastConfig = {
        show: true,
        message,
        type,
        title,
        duration
      };

      // 自动隐藏
      if (duration > 0) {
        setTimeout(() => {
          this.hideToast();
        }, duration);
      }
    },

    // 显示警告提示
    showWarningToast(message, title = '警告') {
      this.showToast(message, title, 'warning');
    }
  }
};

// ===========================================
// 窗口控制混入 (WindowControlMixin)
// ===========================================
// 名称: WindowControlMixin
// 使用的组件: Login.vue, Option.vue
// 功能: 提供Electron窗口的最小化、最大化、关闭等操作
export const WindowControlMixin = {
  methods: {
    // 统一的关闭处理（用于兼容不同的命名）
    handleClose() {
      this.handleCloseApp();
    },

    // 处理关闭应用
    handleCloseApp() {
      console.log('关闭应用', { isElectron: !!window.electronAPI });
      if (window.electronAPI) {
        window.electronAPI.closeApp();
      } else {
        console.warn('Electron API不可用，尝试window.close()');
        try {
          window.close();
        } catch (closeError) {
          console.error('无法关闭应用:', closeError);
        }
      }
    },

    // 处理最大化/还原
    handleMaximize() {
      console.log('最大化/还原窗口', { isElectron: !!window.electronAPI });
      if (window.electronAPI) {
        window.electronAPI.maximizeWindow();
      } else {
        console.warn('Electron API不可用');
      }
    },

    // 处理最小化
    handleMinimize() {
      console.log('最小化窗口', { isElectron: !!window.electronAPI });
      if (window.electronAPI) {
        window.electronAPI.minimizeWindow();
      } else {
        console.warn('Electron API不可用');
      }
    }
  }
};