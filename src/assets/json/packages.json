{"code": 200, "message": "获取套餐数据成功", "timestamp": "2024-01-15T10:30:00.000Z", "data": {"currentPlan": {"name": "免费版", "remaining": 3, "total": 5, "expireDate": "永久有效"}, "packages": [{"id": "basic", "name": "基础版", "price": 29, "originalPrice": 39, "usage": "50次/月使用次数", "popular": false, "type": "basic", "features": ["面试题库访问", "基础数据分析", "邮件支持"], "description": "适合初级求职者的基础功能套餐", "validity": "30天", "discount": 25}, {"id": "premium", "name": "豪华版", "price": 59, "originalPrice": 79, "usage": "150次/月使用次数", "popular": true, "type": "premium", "features": ["包含基础版所有功能", "AI模拟面试", "个性化简历模板", "优先客服支持", "职业发展建议"], "description": "最受欢迎的全功能套餐，性价比最高", "validity": "30天", "discount": 25}, {"id": "ultimate", "name": "终极版", "price": 99, "originalPrice": 129, "usage": "无限次数使用", "popular": false, "type": "ultimate", "features": ["包含豪华版所有功能", "专属面试官指导", "行业专家咨询", "定制化职业规划", "1对1专属服务"], "description": "顶级套餐，享受最专业的求职服务", "validity": "30天", "discount": 23}, {"id": "student", "name": "学生版", "price": 19, "originalPrice": 29, "usage": "30次/月使用次数", "popular": false, "type": "student", "features": ["AI简历优化", "校招面试题库", "实习机会推荐", "学生专属模板"], "description": "专为在校学生设计的优惠套餐", "validity": "30天", "discount": 34}, {"id": "enterprise", "name": "企业版", "price": 299, "originalPrice": 399, "usage": "无限次数 + 团队管理", "popular": false, "type": "enterprise", "features": ["包含终极版所有功能", "团队账户管理", "批量简历处理", "企业定制服务", "专属客户经理", "数据报表分析"], "description": "为企业HR和团队提供的专业解决方案", "validity": "30天", "discount": 25}], "paymentMethods": [{"id": "wechat", "name": "微信支付", "description": "使用微信扫码支付", "icon": "/icons/weixinzhifu.png", "enabled": true, "fee": 0, "minAmount": 1, "maxAmount": 5000}, {"id": "alipay", "name": "支付宝", "description": "使用支付宝扫码支付", "icon": "/icons/zhifubaozhifu.png", "enabled": true, "fee": 0, "minAmount": 1, "maxAmount": 5000}, {"id": "card", "name": "银行卡", "description": "支持各大银行卡支付", "icon": "/icons/yinhangkazhifu.png", "enabled": true, "fee": 0, "minAmount": 1, "maxAmount": 10000}], "rechargeHistory": [{"id": 1, "orderId": "ORD202401150001", "date": "2024-01-15", "packageId": "premium", "packageName": "豪华版", "amount": 59, "originalAmount": 79, "paymentMethod": "wechat", "status": "success", "transactionId": "TXN20240115001", "createTime": "2024-01-15T14:30:00.000Z", "payTime": "2024-01-15T14:31:25.000Z"}, {"id": 2, "orderId": "ORD202312200001", "date": "2023-12-20", "packageId": "basic", "packageName": "基础版", "amount": 29, "originalAmount": 39, "paymentMethod": "alipay", "status": "success", "transactionId": "TXN20231220001", "createTime": "2023-12-20T09:15:00.000Z", "payTime": "2023-12-20T09:16:42.000Z"}]}}