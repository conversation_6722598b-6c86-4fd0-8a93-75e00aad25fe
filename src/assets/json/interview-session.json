{"code": 200, "message": "面试会话数据获取成功", "timestamp": "2024-01-15T14:30:00.000Z", "data": {"sessionInfo": {"sessionId": "session_20240115_143000", "userId": "user_12345", "startTime": "2024-01-15T14:30:00.000Z", "endTime": null, "duration": "00:15:30", "status": "active", "audioSource": "tencent-meeting", "language": "zh-CN", "suggestionLevel": "detailed"}, "userQuota": {"planType": "premium", "totalQuota": 150, "usedQuota": 135, "remainingQuota": 15, "resetDate": "2024-02-15T00:00:00.000Z"}, "sessionStats": {"transcriptWordCount": 1256, "questionCount": 8, "suggestionCount": 24, "averageResponseTime": 1.6, "confidenceScore": 0.89}, "recentTranscript": [{"id": "transcript_001", "type": "interviewer", "speaker": "面试官", "text": "请先自我介绍一下，包括你的教育背景和工作经历。", "timestamp": "14:30:15", "confidence": 0.95, "duration": 3.2}, {"id": "transcript_002", "type": "candidate", "speaker": "我", "text": "好的，我叫张三，毕业于清华大学计算机科学与技术专业，目前有3年的软件开发经验...", "timestamp": "14:30:45", "confidence": 0.92, "duration": 15.6}, {"id": "transcript_003", "type": "interviewer", "speaker": "面试官", "text": "你在之前的工作中遇到过什么技术难题吗？是如何解决的？", "timestamp": "14:32:10", "confidence": 0.94, "duration": 4.1}], "activeSuggestions": [{"id": "active_suggestion_1", "questionId": "q_20240115_143210", "title": "技术问题解决思路", "content": "建议使用STAR法则来回答：描述具体情况(Situation)、任务要求(Task)、采取的行动(Action)和最终结果(Result)。重点展现你的分析能力、解决问题的方法和学习能力。", "confidence": 91, "generatedAt": "14:32:12", "category": "technical"}], "settings": {"audioSettings": {"noiseReduction": true, "autoGainControl": true, "echoCancellation": true, "sampleRate": 16000}, "transcriptionSettings": {"realTimeTranscription": true, "punctuationEnabled": true, "speakerDiarization": true, "confidenceThreshold": 0.8}, "suggestionSettings": {"autoGenerate": true, "suggestionDelay": 2.0, "maxSuggestions": 3, "includeReferences": true}}}}