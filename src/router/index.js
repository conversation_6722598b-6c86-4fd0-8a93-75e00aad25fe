import { createRouter, createWebHistory } from 'vue-router'
import Login from '../components/login/login.vue'
import optionRoutes from './option.js'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录 - OfferHub'
    }
  },
  // 引用option模块的路由配置
  optionRoutes
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局导航守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title
  } else {
    document.title = 'OfferHub - 专业职场助手'
  }
  next()
})

export default router 