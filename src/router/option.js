import Option from '../components/option/option.vue'
import Money from '../components/money/money.vue'
import HomeNested from '../components/homeNested/homeNested.vue'
import Interview from '../components/interview/Interview.vue'
import Summary from '../components/summary/summary.vue'

const optionRoutes = {
  path: '/option',
  name: 'Option',
  component: Option,
  children: [
    {
      path: '',
      name: 'OptionDefault',
      redirect: 'interview'
    },
    {
      path: 'interview',
      name: 'Interview',
      component: Interview,
      meta: {
        title: '辅助面试 - OfferHub',
        menuKey: 'interview',
        menuName: '辅助面试',
        order: 5,
      }
    },
    {
      path: 'summary',
      name: 'Summary',
      component: Summary,
      meta: {
        title: '面试总结 - OfferHub',
        menuKey: 'summary',
        menuName: '面试总结',
        order: 10,
      }
    },
    {
      path: 'money',
      name: 'Money',
      component: Money,
      meta: {
        title: '会员充值 - OfferHub',
        menuKey: 'money',
        menuName: '充值',
        order: 15,
      }
    },
    {
      path: 'home',
      name: 'Home',
      component: HomeNested,
      meta: {
        title: '个人中心 - OfferHub',
        menuKey: 'home',
        menuName: '个人中心',
        order: 20,
      }
    }
  ]
}

export default optionRoutes