<template>
    <div
    :class="[
      customClass || 'modern-select',
      { 'open': isOpen, 'disabled': disabled, 'error': error, 'success': success },
      size ? `size-${size}` : ''
    ]"
    @click.stop="toggle"
    ref="selectContainer"
  >
    <!-- 选择框触发器 -->
    <div 
      class="select-trigger" 
      :class="{ 'focused': isOpen }"
      tabindex="0"
      @keydown="handleKeydown"
      @blur="handleBlur"
    >
      <span 
        class="select-value" 
        :class="{ 'placeholder': !selectedLabel }"
      >
        {{ selectedLabel || placeholder }}
      </span>
      
      <div class="select-arrow">
        <svg viewBox="0 0 20 20">
          <path fill="currentColor" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"/>
        </svg>
      </div>
    </div>
    
    <!-- 下拉菜单 -->
    <div 
      class="select-dropdown" 
      :class="{ 'show': isOpen }"
      ref="dropdown"
    >
      <ul class="select-options">
        <li 
          v-for="(option, index) in options" 
          :key="getOptionKey(option, index)"
          class="select-option"
          :class="{ 
            'selected': isSelected(option),
            'disabled': isOptionDisabled(option)
          }"
          @click.stop="selectOption(option)"
          @mouseenter="hoveredIndex = index"
        >
          {{ getOptionLabel(option) }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SelectBox',
  emits: ['update:modelValue', 'change'],
  props: {
    // 选项列表，支持字符串数组或对象数组
    options: {
      type: Array,
      default: () => []
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 当前选中值 (v-model)
    modelValue: {
      type: [String, Number, Object],
      default: null
    },
    // 对象数组时，用作显示文本的key
    labelKey: {
      type: String,
      default: 'label'
    },
    // 对象数组时，用作值的key
    valueKey: {
      type: String,
      default: 'value'
    },
    // 对象数组时，用作禁用状态的key
    disabledKey: {
      type: String,
      default: 'disabled'
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    },
    // 尺寸: sm, default, lg
    size: {
      type: String,
      default: '',
      validator: value => ['', 'sm', 'lg'].includes(value)
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false
    },
    // 错误状态
    error: {
      type: Boolean,
      default: false
    },
    // 成功状态
    success: {
      type: Boolean,
      default: false
    },
    // 是否可搜索
    searchable: {
      type: Boolean,
      default: false
    },
    // 搜索占位符
    searchPlaceholder: {
      type: String,
      default: '搜索...'
    }
  },
  data() {
    return {
      isOpen: false,
      hoveredIndex: -1,
      searchText: ''
    }
  },
  computed: {
    selectedLabel() {
      if (!this.modelValue) return '';
      
      const option = this.options.find(opt => {
        if (typeof opt === 'object') {
          return opt[this.valueKey] === this.modelValue;
        }
        return opt === this.modelValue;
      });
      
      if (option) {
        return typeof option === 'object' ? option[this.labelKey] : option;
      }
      
      return this.modelValue;
    },
    
    filteredOptions() {
      if (!this.searchable || !this.searchText) {
        return this.options;
      }
      
      return this.options.filter(option => {
        const label = this.getOptionLabel(option).toLowerCase();
        return label.includes(this.searchText.toLowerCase());
      });
    }
  },
  mounted() {
    // 点击外部关闭下拉
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    toggle() {
      if (this.disabled) return;
      this.isOpen = !this.isOpen;
      
      if (this.isOpen) {
        this.$nextTick(() => {
          this.scrollToSelected();
          if (this.searchable) {
            this.$refs.searchInput?.focus();
          }
        });
      }
    },
    
    close() {
      this.isOpen = false;
      this.searchText = '';
      this.hoveredIndex = -1;
    },
    
    selectOption(option) {
      if (this.isOptionDisabled(option)) return;
      
      const value = typeof option === 'object' ? option[this.valueKey] : option;
      this.$emit('update:modelValue', value);
      this.$emit('change', value, option);
      this.close();
    },
    
    getOptionLabel(option) {
      if (typeof option === 'object') {
        return option[this.labelKey] || '';
      }
      return String(option);
    },
    
    getOptionValue(option) {
      if (typeof option === 'object') {
        return option[this.valueKey];
      }
      return option;
    },
    
    getOptionKey(option, index) {
      return this.getOptionValue(option) || index;
    },
    
    isSelected(option) {
      const optionValue = this.getOptionValue(option);
      return optionValue === this.modelValue;
    },
    
    isOptionDisabled(option) {
      if (typeof option === 'object') {
        return option[this.disabledKey] || false;
      }
      return false;
    },
    
    handleClickOutside(event) {
      if (!this.$refs.selectContainer?.contains(event.target)) {
        this.close();
      }
    },
    
    handleKeydown(event) {
      if (this.disabled) return;
      
      switch (event.key) {
        case 'Enter':
        case ' ':
          event.preventDefault();
          if (!this.isOpen) {
            this.toggle();
          } else if (this.hoveredIndex >= 0) {
            this.selectOption(this.filteredOptions[this.hoveredIndex]);
          }
          break;
          
        case 'Escape':
          this.close();
          break;
          
        case 'ArrowDown':
          event.preventDefault();
          if (!this.isOpen) {
            this.toggle();
          } else {
            this.hoveredIndex = Math.min(this.hoveredIndex + 1, this.filteredOptions.length - 1);
            this.scrollToHovered();
          }
          break;
          
        case 'ArrowUp':
          event.preventDefault();
          if (this.isOpen) {
            this.hoveredIndex = Math.max(this.hoveredIndex - 1, 0);
            this.scrollToHovered();
          }
          break;
      }
    },
    
    handleBlur() {
      // 延迟关闭，允许点击选项
      setTimeout(() => {
        if (!this.$refs.selectContainer?.contains(document.activeElement)) {
          this.close();
        }
      }, 200); // 增加延迟时间
    },
    
    scrollToSelected() {
      const selectedIndex = this.filteredOptions.findIndex(option => this.isSelected(option));
      if (selectedIndex >= 0) {
        this.hoveredIndex = selectedIndex;
        this.scrollToHovered();
      }
    },
    
    scrollToHovered() {
      this.$nextTick(() => {
        const dropdown = this.$refs.dropdown;
        const options = dropdown?.querySelectorAll('.select-option');
        const hoveredOption = options?.[this.hoveredIndex];
        
        if (hoveredOption && dropdown) {
          const optionTop = hoveredOption.offsetTop;
          const optionBottom = optionTop + hoveredOption.offsetHeight;
          const dropdownTop = dropdown.scrollTop;
          const dropdownBottom = dropdownTop + dropdown.clientHeight;
          
          if (optionTop < dropdownTop) {
            dropdown.scrollTop = optionTop;
          } else if (optionBottom > dropdownBottom) {
            dropdown.scrollTop = optionBottom - dropdown.clientHeight;
          }
        }
      });
    }
  }
}
</script>

<style scoped>
@import './selectBox.scss';
</style>