// 全局弹窗样式文件

// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$black: #181818;
$text-secondary: #64748b;
$transition: 0.3s;
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-error: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
$gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

/*
使用示例：

<!-- 通用弹窗结构 -->
<div class="popup-overlay" :class="{ show: showPopup }">
  <div class="popup-dialog">
    <div class="popup-icon success"></div>
    <h3 class="popup-title">操作成功</h3>
    <p class="popup-message">您的操作已经成功完成！</p>
    <button class="popup-btn primary" @click="closePopup">确定</button>
  </div>
</div>

<!-- 兼容性：继续支持原有的success-* 类名 -->
<div class="success-overlay" :class="{ show: showSuccessDialog }">
  <div class="success-dialog">
    <div class="success-icon"></div>
    <h3 class="success-title">支付成功</h3>
    <p class="success-message">恭喜您成功开通豪华版！</p>
    <button class="success-btn" @click="closeSuccessDialog">我知道了</button>
  </div>
</div>

弹窗类型：
- .popup-icon.success - 成功图标
- .popup-icon.error - 错误图标 
- .popup-icon.warning - 警告图标
- .popup-icon.info - 信息图标

按钮类型：
- .popup-btn.primary - 主要按钮
- .popup-btn.success - 成功按钮
- .popup-btn.error - 错误按钮
- .popup-btn.warning - 警告按钮
- .popup-btn.secondary - 次要按钮
*/

// 通用弹窗覆盖层
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all $transition ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// 兼容性 - 成功弹窗覆盖层别名
.success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all $transition ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// 通用弹窗对话框
.popup-dialog {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 40px 30px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 0 48px 24px rgba(255, 255, 255, 0.55);
  transform: scale(0.9);
  transition: transform $transition ease;
  
  .popup-overlay.show &,
  .success-overlay.show & {
    transform: scale(1);
  }
}

// 兼容性 - 成功弹窗对话框别名
.success-dialog {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 40px 30px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 0 48px 24px rgba(255, 255, 255, 0.55);
  transform: scale(0.9);
  transition: transform $transition ease;
  
  .success-overlay.show & {
    transform: scale(1);
  }
}

// 错误弹窗覆盖层
.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all $transition ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// 错误弹窗对话框
.error-dialog {
  background-color: $neu-1;
  border-radius: 20px;
  padding: 40px 30px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 0 48px 24px rgba(255, 255, 255, 0.55);
  transform: scale(0.9);
  transition: transform $transition ease;
  
  .error-overlay.show & {
    transform: scale(1);
  }
}

// 通用弹窗图标样式
.popup-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  
  // 成功图标
  &.success {
    background-color: $neu-1;
    
    &::before {
      content: '';
      width: 60px;
      height: 60px;
      background-image: url("/icons/chenggong.png");
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  
  // 错误图标
  &.error {
    background-color: $neu-1;
    
    &::before {
      content: '';
      width: 60px;
      height: 60px;
      background-image: url("/icons/cuowu.png");
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  
  // 警告图标
  &.warning {
    background-color: $neu-1;
    
    &::before {
      content: '';
      width: 60px;
      height: 60px;
      background-image: url("/icons/zhuyi.png");
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  
  // 信息图标
  &.info {
    background-color: $neu-1;
    
    &::before {
      content: '';
      width: 60px;
      height: 60px;
      background-image: url("/icons/xinxi.png");
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}

// 兼容性 - 成功图标别名
.success-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  background-color: $neu-1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  
  &::before {
    content: '';
    width: 60px;
    height: 60px;
    background-image: url("/icons/chenggong.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

// 通用弹窗标题样式
.popup-title {
  font-size: 24px;
  font-weight: 700;
  color: $black;
  margin-bottom: 15px;
  cursor: default;
  user-select: none;
  text-align: center;
}

// 通用弹窗消息样式
.popup-message {
  font-size: 16px;
  color: $text-secondary;
  line-height: 1.6;
  margin-bottom: 30px;
  cursor: default;
  user-select: none;
  text-align: center;
}

// 通用弹窗按钮样式
.popup-btn {
  padding: 14px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all $transition ease;
  box-shadow:
    6px 6px 12px $neu-2,
    -6px -6px 12px $white;
  outline: none;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  // 主要按钮
  &.primary {
    background: $gradient-primary;
    color: white;
  }
  
  // 成功按钮
  &.success {
    background: $gradient-success;
    color: white;
  }
  
  // 错误按钮
  &.error {
    background: $gradient-error;
    color: white;
  }
  
  // 警告按钮
  &.warning {
    background: $gradient-warning;
    color: white;
  }
  
  // 次要按钮
  &.secondary {
    background: $neu-1;
    color: $text-secondary;
    box-shadow:
      6px 6px 12px $neu-2,
      -6px -6px 12px $white;
  }
}

// 兼容性别名 - 成功标题
.success-title {
  font-size: 24px;
  font-weight: 700;
  color: $black;
  margin-bottom: 15px;
  cursor: default;
  user-select: none;
}

// 兼容性别名 - 成功消息
.success-message {
  font-size: 16px;
  color: $text-secondary;
  line-height: 1.6;
  margin-bottom: 30px;
  cursor: default;
  user-select: none;
}

// 兼容性别名 - 成功按钮
.success-btn {
  padding: 14px 32px;
  border: none;
  border-radius: 12px;
  background: $gradient-primary;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all $transition ease;
  box-shadow:
    6px 6px 12px $neu-2,
    -6px -6px 12px $white;
  outline: none;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 错误弹窗元素样式
.error-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  background-color: $neu-1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    8px 8px 16px $neu-2,
    -8px -8px 16px $white;
  
  &::before {
    content: '';
    width: 60px;
    height: 60px;
    background-image: url("/icons/cuowu.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

.error-title {
  font-size: 24px;
  font-weight: 700;
  color: $black;
  margin-bottom: 15px;
  cursor: default;
  user-select: none;
}

.error-message {
  font-size: 16px;
  color: $text-secondary;
  line-height: 1.6;
  margin-bottom: 30px;
  cursor: default;
  user-select: none;
}

.error-btn {
  padding: 14px 32px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all $transition ease;
  box-shadow:
    6px 6px 12px $neu-2,
    -6px -6px 12px $white;
  outline: none;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      8px 8px 16px $neu-2,
      -8px -8px 16px $white;
  }
  
  &:active {
    transform: translateY(0);
  }
}