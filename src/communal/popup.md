# 弹窗样式使用指南

## 📋 弹窗分类原则

### 🎯 **popupPoge.scss** - 需要用户点击交互的弹窗
**使用场景**: 确认对话框、表单弹窗、详细信息弹窗等需要用户主动操作的界面

**特征**:
- 需要用户点击按钮（确定、取消、关闭等）
- 包含表单输入
- 确认/选择操作
- 模态对话框
- 阻塞用户操作直到处理完成

### 🌊 **popupSmooth.scss** - 不需要点击交互的弹窗  
**使用场景**: Toast 提示、加载状态、自动通知等自动消失或无需交互的提示

**特征**:
- 自动显示和消失
- 不阻塞用户操作
- 仅用于信息展示
- 无需用户点击
- 悬浮提示

---

## 🗂️ 当前项目中的弹窗分类

### 📌 **使用 popupPoge.scss** 的组件

#### **HomeNested 组件**
- ✅ **修改个人信息弹窗** (`showInfoDialog`)
  - 包含表单输入（姓名、性别、求职状态）
  - 需要点击"保存修改"或"取消"
  
- ✅ **修改密码弹窗** (`showPasswordDialog`)
  - 包含密码输入表单
  - 需要点击"确认修改"或"取消"
  
- ✅ **通知弹窗** (`showNotification`)
  - 需要点击"知道了"按钮
  
- ✅ **退出登录确认** (`showLogoutConfirm`)
  - 需要点击"确定"或"取消"
  
- ✅ **路由切换确认** (`showRouteConfirm`)
  - 需要点击"确定离开"或"继续编辑"

#### **Money 组件**
- ✅ **支付成功弹窗** (`showSuccessDialog`)
  - 需要点击"我知道了"

#### **Interview 组件**
- ✅ **伦理声明弹窗** (`showEthicsModal`)
  - 需要点击"我知道了"或"同意并继续"

### 🌟 **使用 popupSmooth.scss** 的组件

#### **HomeNested 组件**
- ✅ **Toast 提示** (`toastConfig`)
  - 保存成功/失败提示
  - 自动 3 秒消失
  - 无需用户点击

#### **所有组件 (通过 ToastMixin)**
- ✅ **成功提示** (`showSuccessToast`)
- ✅ **错误提示** (`showErrorToast`) 
- ✅ **警告提示** (`showWarningToast`)
- ✅ **信息提示** (`showInfoToast`)

---

## 🛠️ 使用方法

### **引入 popupPoge.scss**
```scss
@import '../../communal/popupPoge.scss';
```

**适用于**: Money.vue、需要确认操作的弹窗

### **引入 popupSmooth.scss**  
```scss
@import '../../communal/popupSmooth.scss';
```

**适用于**: Toast 提示、Loading 状态、自动通知

### **同时引入两者**
```scss
@import '../../communal/popupPoge.scss';
@import '../../communal/popupSmooth.scss';
```

**适用于**: HomeNested.vue（既有确认弹窗又有 Toast 提示）

---

## 📝 开发建议

1. **新增弹窗时先确定类型**:
   - 需要用户点击 → 使用 `popupPoge.scss`
   - 自动显示消失 → 使用 `popupSmooth.scss`

2. **保持一致性**:
   - 同类型弹窗使用相同的样式文件
   - 遵循现有的命名规范

3. **性能考虑**:
   - 只引入需要的样式文件
   - 避免重复引入

4. **用户体验**:
   - Toast 提示不应该阻塞用户操作
   - 确认弹窗应该清晰明确地表达后果