// 下拉选择框样式 - 适配原有外框样式

// 变量定义
$select-primary: #6366f1;
$select-primary-hover: #5b5fc7;
$select-bg: #ffffff;
$select-border: #e5e7eb;
$select-border-focus: #d1d5db;
$select-text: #374151;
$select-text-secondary: #6b7280;
$select-text-placeholder: #9ca3af;
$select-hover-bg: #f9fafb;
$select-active-bg: #f3f4f6;
$select-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$select-shadow-focus: 0 0 0 3px rgba(99, 102, 241, 0.1);
$select-shadow-dropdown: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$select-radius: 8px;
$select-radius-sm: 6px;
$select-transition: all 0.15s ease-in-out;

// 通用下拉框样式 - 适配任何外框样式
.modern-select,
.form-select,
.edit-select,
.setting-select,
.region-selector {
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  // 隐藏原生select的箭头
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  
  .select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: $select-transition;
    user-select: none;
    font-size: inherit;
    color: inherit;
    padding: 0;
    outline: none;
    
    &:hover {
      background: transparent;
    }
    
    &:focus {
      outline: none;
    }
  }
  
  // 选中的值显示
  .select-value {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: inherit;
    
    &.placeholder {
      color: $select-text-placeholder;
    }
  }
  
  // 下拉箭头
  .select-arrow {
    width: 18px;
    height: 18px;
    margin-left: 8px;
    transition: transform $select-transition;
    color: $select-text-secondary;
    flex-shrink: 0;
    
    svg {
      width: 100%;
      height: 100%;
      stroke: currentColor;
      stroke-width: 2;
      fill: none;
    }
  }
  
  // 展开状态
  &.open {
    .select-arrow {
      transform: rotate(180deg);
    }
  }
  
  // 下拉选项容器
  .select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: $select-bg;
    border: 1px solid $select-border;
    border-radius: $select-radius;
    box-shadow: $select-shadow-dropdown;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all $select-transition;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 4px;
    pointer-events: none;
    
    &.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
      pointer-events: auto;
    }
  }
  
  // 选项列表
  .select-options {
    list-style: none;
    margin: 0;
    padding: 4px 0;
  }
  
  // 单个选项
  .select-option {
    position: relative;
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color $select-transition;
    color: $select-text;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &:hover {
      background: $select-hover-bg;
    }
    
    &.selected {
      background: $select-active-bg;
      color: $select-primary;
      font-weight: 500;
      
      &::after {
        content: '✓';
        color: $select-primary;
        font-weight: bold;
      }
    }
    
    &:active {
      background: $select-active-bg;
    }
  }
  
  // 禁用状态
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
    
    .select-trigger {
      cursor: not-allowed;
    }
  }
  
  // 错误状态
  &.error {
    .select-trigger {
      border-color: #ef4444;
    }
  }
  
  // 成功状态
  &.success {
    .select-trigger {
      border-color: #10b981;
    }
  }
  
  // 尺寸变体
  &.size-sm {
    .select-trigger {
      min-height: 32px;
      font-size: 12px;
    }
    
    .select-option {
      padding: 6px 8px;
      font-size: 12px;
    }
  }
  
  &.size-lg {
    .select-trigger {
      min-height: 48px;
      font-size: 16px;
    }
    
    .select-option {
      padding: 12px 16px;
      font-size: 16px;
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .modern-select,
  .form-select,
  .edit-select,
  .setting-select,
  .region-selector {
    .select-dropdown {
      background: #1f2937;
      border-color: #374151;
    }
    
    .select-option {
      color: #f9fafb;
      
      &:hover {
        background: #374151;
      }
      
      &.selected {
        background: #4b5563;
        color: #60a5fa;
      }
    }
  }
}