// 平滑弹窗样式文件 - 用于不需要点击交互的提示弹窗
// 适用场景：Toast提示、加载提示、自动消失的通知等

// 内联的变量定义
$neu-1: #ecf0f3;
$neu-2: #d1d9e6;
$white: #f9f9f9;
$black: #181818;
$text-primary: #374151;
$text-secondary: #64748b;
$text-muted: #9ca3af;
$transition: 0.3s;
$success-color: #10b981;
$error-color: #ef4444;
$warning-color: #f59e0b;
$info-color: #3b82f6;

/*
使用示例：

<!-- Toast 提示弹窗 -->
<div class="toast-container" :class="{ show: toastConfig.show }">
  <div class="toast" :class="toastConfig.type">
    <div class="toast-icon">
      <img src="/icons/chenggong.png" alt="成功" />
    </div>
    <div class="toast-content">
      <div class="toast-title">{{ toastConfig.title }}</div>
      <div class="toast-message">{{ toastConfig.message }}</div>
    </div>
  </div>
</div>

<!-- 加载提示弹窗 -->
<div class="loading-container" :class="{ show: isLoading }">
  <div class="loading-spinner">
    <div class="spinner"></div>
    <div class="loading-text">正在加载...</div>
  </div>
</div>

弹窗类型：
- .toast.success - 成功提示
- .toast.error - 错误提示
- .toast.warning - 警告提示
- .toast.info - 信息提示
*/

// Toast 提示容器 - 右上角滑入式
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
  }

  .toast {
    display: flex;
    align-items: center;
    min-width: 280px;
    max-width: 400px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    backdrop-filter: blur(10px);

    &.success {
      border-left-color: $success-color;
    }

    &.error {
      border-left-color: $error-color;
    }

    &.warning {
      border-left-color: $warning-color;
    }

    &.info {
      border-left-color: $info-color;
    }

    .toast-icon {
      margin-right: 12px;
      flex-shrink: 0;
      
      img {
        width: 24px;
        height: 24px;
      }
    }

    .toast-content {
      flex: 1;

      .toast-title {
        font-weight: 600;
        font-size: 14px;
        color: $text-primary;
        margin-bottom: 4px;
      }

      .toast-message {
        font-size: 13px;
        color: $text-secondary;
        line-height: 1.4;
      }
    }
  }
}

// 加载提示容器 - 中央显示
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: all $transition ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }

  .loading-spinner {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);

    .spinner {
      width: 40px;
      height: 40px;
      margin: 0 auto 20px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid $info-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 14px;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

// 旋转动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 通知栏 - 顶部滑下式
.notification-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9997;
  padding: 12px 20px;
  text-align: center;
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;

  &.show {
    transform: translateY(0);
  }

  &.success {
    background: linear-gradient(135deg, $success-color 0%, #06d6a0 100%);
    color: white;
  }

  &.error {
    background: linear-gradient(135deg, $error-color 0%, #dc2626 100%);
    color: white;
  }

  &.warning {
    background: linear-gradient(135deg, $warning-color 0%, #f97316 100%);
    color: white;
  }

  &.info {
    background: linear-gradient(135deg, $info-color 0%, #2563eb 100%);
    color: white;
  }

  .notification-content {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto;

    .notification-icon {
      margin-right: 8px;
      
      img {
        width: 18px;
        height: 18px;
        filter: brightness(0) invert(1); // 将图标变为白色
      }
    }

    .notification-text {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 角标提示 - 右下角
.badge-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9996;
  min-width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  &.show {
    transform: scale(1);
  }

  &.success {
    background: $success-color;
  }

  &.error {
    background: $error-color;
  }

  &.warning {
    background: $warning-color;
  }

  &.info {
    background: $info-color;
  }

  .badge-icon {
    img {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }

  .badge-count {
    font-size: 18px;
    font-weight: 700;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    
    .toast {
      min-width: auto;
      max-width: none;
    }
  }

  .loading-container {
    .loading-spinner {
      padding: 30px;
      margin: 0 20px;
    }
  }

  .notification-bar {
    padding: 10px 16px;
    
    .notification-content {
      .notification-text {
        font-size: 13px;
      }
    }
  }
}