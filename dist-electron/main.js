"use strict";
const electron = require("electron");
const path = require("path");
if (process.platform === "win32") {
  process.stdout.setDefaultEncoding("utf8");
}
let mainWindow;
function createWindow() {
  mainWindow = new electron.BrowserWindow({
    width: 980,
    height: 580,
    frame: false,
    transparent: true,
    roundedCorners: true,
    titleBarStyle: "hidden",
    titleBarOverlay: false,
    resizable: false,
    minimizable: false,
    maximizable: false,
    closable: true,
    skipTaskbar: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, "preload.js")
    }
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../dist/index.html"));
  }
  mainWindow.on("closed", () => {
    mainWindow = null;
    electron.app.quit();
  });
}
electron.app.whenReady().then(() => {
  createWindow();
  electron.app.on("activate", () => {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.ipcMain.on("close-app", () => {
  console.log("收到关闭应用请求");
  electron.app.quit();
});
electron.ipcMain.on("login-success", () => {
  console.log("收到登录成功消息");
  if (mainWindow) {
    mainWindow.setSize(1440, 1080);
    mainWindow.setResizable(true);
    mainWindow.center();
    console.log("登录成功，窗口已调整到主应用大小");
  } else {
    console.error("主窗口不存在，无法调整大小");
  }
});
electron.ipcMain.on("logout-reset", () => {
  console.log("收到退出登录消息，重置窗口大小");
  if (mainWindow) {
    mainWindow.setSize(980, 580);
    mainWindow.setResizable(false);
    mainWindow.center();
    console.log("窗口已重置到登录页面大小");
  } else {
    console.error("主窗口不存在，无法重置大小");
  }
});
electron.ipcMain.on("resize-window", (event, { width, height }) => {
  if (mainWindow) {
    mainWindow.setSize(width, height);
    mainWindow.center();
  }
});
electron.ipcMain.on("resize-to-option", () => {
  if (mainWindow) {
    mainWindow.setSize(1440, 1080);
    mainWindow.setResizable(true);
    mainWindow.center();
  }
});
electron.ipcMain.on("minimize-window", () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});
electron.ipcMain.on("maximize-window", () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") electron.app.quit();
});
