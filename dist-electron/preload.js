"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("electronAPI", {
  closeApp: () => electron.ipcRenderer.send("close-app"),
  minimizeWindow: () => electron.ipcRenderer.send("minimize-window"),
  maximizeWindow: () => electron.ipcRenderer.send("maximize-window"),
  loginSuccess: () => electron.ipcRenderer.send("login-success"),
  logoutReset: () => electron.ipcRenderer.send("logout-reset"),
  onSetRoute: (callback) => electron.ipcRenderer.on("set-route", callback),
  // 添加环境检测
  isElectron: () => true
});
electron.contextBridge.exposeInMainWorld("isElectron", () => true);
