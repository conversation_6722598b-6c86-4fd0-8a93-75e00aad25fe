import { contextBridge, ipcRenderer } from 'electron'

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  closeApp: () => ipcRenderer.send('close-app'),
  minimizeWindow: () => ipcRenderer.send('minimize-window'),
  maximizeWindow: () => ipcRenderer.send('maximize-window'),
  loginSuccess: () => ipcRenderer.send('login-success'),
  logoutReset: () => ipcRenderer.send('logout-reset'),
  onSetRoute: (callback) => ipcRenderer.on('set-route', callback),
  
  // 添加环境检测
  isElectron: () => true
})

// 为了兼容性，也暴露一些全局函数
contextBridge.exposeInMainWorld('isElectron', () => true)