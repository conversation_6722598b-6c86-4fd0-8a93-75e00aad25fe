import { app, BrowserWindow, ipcMain } from 'electron'
import path from 'path'

// 设置控制台输出编码为UTF-8
if (process.platform === 'win32') {
  process.stdout.setDefaultEncoding('utf8')
}

let mainWindow

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 980, 
    height: 580, 
    frame: false,
    transparent: true,
    roundedCorners: true,
    titleBarStyle: 'hidden',
    titleBarOverlay: false,
    resizable: false,
    minimizable: false,
    maximizable: false,
    closable: true,
    skipTaskbar: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }})

  if (process.env.VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL)
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }
  
  // 监听窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null
    app.quit()
  })
}

app.whenReady().then(() => {
  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// 处理关闭应用请求
ipcMain.on('close-app', () => {
  console.log('收到关闭应用请求')
  app.quit()
})

// 处理登录成功后的窗口调整
ipcMain.on('login-success', () => {
  console.log('收到登录成功消息')
  if (mainWindow) {
    // 调整到主应用窗口大小
    mainWindow.setSize(1440, 1080)
    mainWindow.setResizable(true)
    mainWindow.center()
    console.log('登录成功，窗口已调整到主应用大小')
  } else {
    console.error('主窗口不存在，无法调整大小')
  }
})

// 处理退出登录后的窗口重置
ipcMain.on('logout-reset', () => {
  console.log('收到退出登录消息，重置窗口大小')
  if (mainWindow) {
    // 恢复到初始窗口大小 - 与createWindow()中的设置一致
    mainWindow.setSize(980, 580)
    mainWindow.setResizable(false)
    mainWindow.center()
    console.log('窗口已重置到登录页面大小')
  } else {
    console.error('主窗口不存在，无法重置大小')
  }
})

// 处理窗口大小调整
ipcMain.on('resize-window', (event, { width, height }) => {
  if (mainWindow) {
    mainWindow.setSize(width, height)
    mainWindow.center()
  }
})

// 处理调整到option页面尺寸
ipcMain.on('resize-to-option', () => {
  if (mainWindow) {
    mainWindow.setSize(1440, 1080)
    mainWindow.setResizable(true)
    mainWindow.center()
  }
})

// 处理窗口最小化
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize()
  }
})

// 处理窗口最大化/还原
ipcMain.on('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize()
    } else {
      mainWindow.maximize()
    }
  }
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})