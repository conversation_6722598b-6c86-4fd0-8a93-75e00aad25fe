<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" viewBox="0 0 900 700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ecf0f3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9f9f9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="excellentScore" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="primaryBtn" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4B70E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b5bdb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="secondaryBtn" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ecf0f3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d1d9e6;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="modalShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="20" dy="20" stdDeviation="20" flood-color="rgba(0,0,0,0.1)" flood-opacity="1"/>
      <feDropShadow dx="-20" dy="-20" stdDeviation="20" flood-color="#ffffff" flood-opacity="0.8"/>
    </filter>
    
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="4" dy="4" stdDeviation="4" flood-color="#d1d9e6" flood-opacity="0.6"/>
    </filter>
  </defs>
  
  <!-- 背景遮罩 -->
  <rect width="900" height="700" fill="rgba(0,0,0,0.5)"/>
  
  <!-- 模态框 -->
  <rect x="50" y="50" width="800" height="600" rx="16" fill="#f9f9f9" filter="url(#modalShadow)"/>
  
  <!-- 模态框内容 -->
  <g transform="translate(74, 74)">
    <!-- 标题栏 -->
    <text x="0" y="24" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#1e293b">面试详情</text>
    
    <!-- 关闭按钮 -->
    <circle cx="728" cy="16" r="16" fill="#ecf0f3"/>
    <text x="728" y="22" font-family="Arial, sans-serif" font-size="18" fill="#64748b" text-anchor="middle">×</text>
    
    <!-- 基本信息区域 -->
    <g transform="translate(0, 60)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#1e293b">基本信息</text>
      <line x1="0" y1="8" x2="752" y2="8" stroke="#ecf0f3" stroke-width="2"/>
      
      <!-- 信息网格 -->
      <g transform="translate(0, 24)">
        <text x="0" y="0" font-family="Arial, sans-serif" font-size="12" fill="#64748b">面试时间</text>
        <text x="0" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#1e293b">2024-01-15 14:30---15:45</text>
        
        <text x="300" y="0" font-family="Arial, sans-serif" font-size="12" fill="#64748b">面试时长</text>
        <text x="300" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#1e293b">01:15:00</text>
      </g>
    </g>
    
    <!-- 面试表现区域 -->
    <g transform="translate(0, 160)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#1e293b">面试表现</text>
      <line x1="0" y1="8" x2="752" y2="8" stroke="#ecf0f3" stroke-width="2"/>
      
      <!-- 表现网格 -->
      <g transform="translate(0, 24)">
        <!-- 转写字数 -->
        <rect x="0" y="0" width="120" height="60" rx="12" fill="#ecf0f3" filter="url(#cardShadow)"/>
        <text x="60" y="20" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">转写字数</text>
        <text x="60" y="40" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#1e293b" text-anchor="middle">2156</text>
        
        <!-- 问题数量 -->
        <rect x="140" y="0" width="120" height="60" rx="12" fill="#ecf0f3" filter="url(#cardShadow)"/>
        <text x="200" y="20" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">问题数量</text>
        <text x="200" y="40" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#1e293b" text-anchor="middle">12</text>
        
        <!-- 建议生成 -->
        <rect x="280" y="0" width="120" height="60" rx="12" fill="#ecf0f3" filter="url(#cardShadow)"/>
        <text x="340" y="20" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">建议生成</text>
        <text x="340" y="40" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#1e293b" text-anchor="middle">28</text>
        
        <!-- 综合评分 -->
        <rect x="420" y="0" width="120" height="60" rx="12" fill="#ecf0f3" filter="url(#cardShadow)"/>
        <text x="480" y="20" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">综合评分</text>
        <text x="480" y="40" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#4B70E2" text-anchor="middle">85</text>
      </g>
    </g>
    
    <!-- 面试亮点区域 -->
    <g transform="translate(0, 280)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#1e293b">面试亮点</text>
      <line x1="0" y1="8" x2="752" y2="8" stroke="#ecf0f3" stroke-width="2"/>
      
      <!-- 亮点列表 -->
      <g transform="translate(0, 24)">
        <rect x="0" y="0" width="752" height="32" rx="8" fill="#ecf0f3"/>
        <circle cx="16" cy="16" r="3" fill="#10b981"/>
        <text x="32" y="20" font-family="Arial, sans-serif" font-size="14" fill="#1e293b">技术深度表现优秀</text>
        
        <rect x="0" y="40" width="752" height="32" rx="8" fill="#ecf0f3"/>
        <circle cx="16" cy="56" r="3" fill="#10b981"/>
        <text x="32" y="60" font-family="Arial, sans-serif" font-size="14" fill="#1e293b">项目经验丰富</text>
        
        <rect x="0" y="80" width="752" height="32" rx="8" fill="#ecf0f3"/>
        <circle cx="16" cy="96" r="3" fill="#10b981"/>
        <text x="32" y="100" font-family="Arial, sans-serif" font-size="14" fill="#1e293b">沟通表达清晰</text>
      </g>
    </g>
    
    <!-- 改进建议区域 -->
    <g transform="translate(0, 420)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#1e293b">改进建议</text>
      <line x1="0" y1="8" x2="752" y2="8" stroke="#ecf0f3" stroke-width="2"/>
      
      <!-- 建议列表 -->
      <g transform="translate(0, 24)">
        <rect x="0" y="0" width="752" height="32" rx="8" fill="#ecf0f3"/>
        <circle cx="16" cy="16" r="3" fill="#f59e0b"/>
        <text x="32" y="20" font-family="Arial, sans-serif" font-size="14" fill="#1e293b">算法题解答可以更简洁</text>
        
        <rect x="0" y="40" width="752" height="32" rx="8" fill="#ecf0f3"/>
        <circle cx="16" cy="56" r="3" fill="#f59e0b"/>
        <text x="32" y="60" font-family="Arial, sans-serif" font-size="14" fill="#1e293b">可以增加对新技术的了解</text>
      </g>
    </g>
  </g>
  
  <!-- 底部按钮 -->
  <g transform="translate(74, 590)">
    <!-- 关闭按钮 -->
    <rect x="580" y="0" width="80" height="36" rx="8" fill="url(#secondaryBtn)" stroke="#d1d9e6" stroke-width="1"/>
    <text x="620" y="22" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#1e293b" text-anchor="middle">关闭</text>
    
    <!-- 导出报告按钮 -->
    <rect x="672" y="0" width="80" height="36" rx="8" fill="url(#primaryBtn)"/>
    <text x="712" y="22" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">导出报告</text>
  </g>
</svg>
