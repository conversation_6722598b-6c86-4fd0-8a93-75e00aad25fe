<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ecf0f3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9f9f9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="currentBadge" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="primaryBtn" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4B70E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b5bdb;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="8" dy="8" stdDeviation="8" flood-color="#d1d9e6" flood-opacity="0.8"/>
      <feDropShadow dx="-8" dy="-8" stdDeviation="8" flood-color="#ffffff" flood-opacity="0.8"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="url(#bgGradient)"/>
  
  <!-- 主容器 -->
  <g transform="translate(24, 24)">
    <!-- 页面标题 -->
    <text x="0" y="32" font-family="Arial, sans-serif" font-size="28" font-weight="700" fill="#1e293b">面试总结</text>
    <text x="0" y="56" font-family="Arial, sans-serif" font-size="16" fill="#64748b">查看和管理您的历史面试记录</text>
    
    <!-- 当前面试总结区域 -->
    <g transform="translate(0, 88)">
      <!-- 区域标题和徽章 -->
      <text x="0" y="24" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#1e293b">当前面试总结</text>
      <rect x="160" y="8" width="80" height="24" rx="12" fill="url(#currentBadge)"/>
      <text x="200" y="24" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="white" text-anchor="middle">刚刚完成</text>
      
      <!-- 当前面试卡片 -->
      <rect x="0" y="40" width="752" height="140" rx="16" fill="#f9f9f9" filter="url(#cardShadow)" stroke="#4B70E2" stroke-width="2"/>
      
      <!-- 面试信息 -->
      <text x="20" y="70" font-family="Arial, sans-serif" font-size="17" font-weight="600" fill="#1e293b">前端开发工程师面试</text>
      <text x="20" y="90" font-family="Arial, sans-serif" font-size="13" fill="#64748b">01:15:00</text>
      
      <!-- 统计信息 -->
      <g transform="translate(20, 100)">
        <!-- 转写字数 -->
        <rect x="0" y="0" width="120" height="60" rx="12" fill="#ecf0f3"/>
        <text x="60" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1e293b" text-anchor="middle">2156</text>
        <text x="60" y="45" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">转写字数</text>
        
        <!-- 问题数量 -->
        <rect x="140" y="0" width="120" height="60" rx="12" fill="#ecf0f3"/>
        <text x="200" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1e293b" text-anchor="middle">12</text>
        <text x="200" y="45" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">问题数量</text>
        
        <!-- 建议生成 -->
        <rect x="280" y="0" width="120" height="60" rx="12" fill="#ecf0f3"/>
        <text x="340" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1e293b" text-anchor="middle">28</text>
        <text x="340" y="45" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">建议生成</text>
        
        <!-- 综合评分 -->
        <rect x="420" y="0" width="120" height="60" rx="12" fill="#ecf0f3"/>
        <text x="480" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1e293b" text-anchor="middle">85</text>
        <text x="480" y="45" font-family="Arial, sans-serif" font-size="12" fill="#64748b" text-anchor="middle">综合评分</text>
      </g>
      
      <!-- 查看详情按钮 -->
      <rect x="632" y="140" width="100" height="32" rx="8" fill="url(#primaryBtn)"/>
      <text x="682" y="160" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">查看详情</text>
    </g>
    
    <!-- 历史面试记录区域 -->
    <g transform="translate(0, 308)">
      <!-- 区域标题 -->
      <text x="0" y="24" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#1e293b">历史面试记录</text>
      
      <!-- 空状态容器 -->
      <g transform="translate(376, 120)">
        <!-- 空状态图标 -->
        <circle cx="0" cy="0" r="40" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="2"/>
        <path d="M-20,-10 L20,-10 L20,10 L-20,10 Z M-15,-5 L15,-5 M-15,0 L10,0 M-15,5 L12,5" 
              stroke="#94a3b8" stroke-width="2" fill="none"/>
        
        <!-- 空状态文字 -->
        <text x="0" y="80" font-family="Arial, sans-serif" font-size="16" fill="#94a3b8" text-anchor="middle">
          暂无历史面试记录
        </text>
      </g>
    </g>
  </g>
</svg>
