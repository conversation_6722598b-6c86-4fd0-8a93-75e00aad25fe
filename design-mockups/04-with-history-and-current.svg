<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="700" viewBox="0 0 800 700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ecf0f3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9f9f9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="currentBadge" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="excellentScore" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="goodScore" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="primaryBtn" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4B70E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b5bdb;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="8" dy="8" stdDeviation="8" flood-color="#d1d9e6" flood-opacity="0.8"/>
      <feDropShadow dx="-8" dy="-8" stdDeviation="8" flood-color="#ffffff" flood-opacity="0.8"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="700" fill="url(#bgGradient)"/>
  
  <!-- 主容器 -->
  <g transform="translate(24, 24)">
    <!-- 页面标题 -->
    <text x="0" y="32" font-family="Arial, sans-serif" font-size="28" font-weight="700" fill="#1e293b">面试总结</text>
    <text x="0" y="56" font-family="Arial, sans-serif" font-size="16" fill="#64748b">查看和管理您的历史面试记录</text>
    
    <!-- 当前面试总结区域 -->
    <g transform="translate(0, 88)">
      <!-- 区域标题和徽章 -->
      <text x="0" y="24" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#1e293b">当前面试总结</text>
      <rect x="160" y="8" width="80" height="24" rx="12" fill="url(#currentBadge)"/>
      <text x="200" y="24" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="white" text-anchor="middle">刚刚完成</text>
      
      <!-- 当前面试卡片 -->
      <rect x="0" y="40" width="752" height="100" rx="16" fill="#f9f9f9" filter="url(#cardShadow)" stroke="#4B70E2" stroke-width="2"/>
      
      <!-- 面试信息 -->
      <text x="20" y="70" font-family="Arial, sans-serif" font-size="17" font-weight="600" fill="#1e293b">全栈开发工程师面试</text>
      <text x="20" y="90" font-family="Arial, sans-serif" font-size="13" fill="#64748b">01:25:00</text>
      
      <!-- 统计信息（简化版） -->
      <g transform="translate(300, 60)">
        <text x="0" y="0" font-family="Arial, sans-serif" font-size="15" font-weight="700" fill="#1e293b">2456</text>
        <text x="0" y="15" font-family="Arial, sans-serif" font-size="11" fill="#64748b">转写字数</text>
        
        <text x="80" y="0" font-family="Arial, sans-serif" font-size="15" font-weight="700" fill="#1e293b">16</text>
        <text x="80" y="15" font-family="Arial, sans-serif" font-size="11" fill="#64748b">问题数量</text>
        
        <text x="160" y="0" font-family="Arial, sans-serif" font-size="15" font-weight="700" fill="#1e293b">32</text>
        <text x="160" y="15" font-family="Arial, sans-serif" font-size="11" fill="#64748b">建议生成</text>
        
        <text x="240" y="0" font-family="Arial, sans-serif" font-size="15" font-weight="700" fill="#1e293b">88</text>
        <text x="240" y="15" font-family="Arial, sans-serif" font-size="11" fill="#64748b">综合评分</text>
      </g>
      
      <!-- 查看详情按钮 -->
      <rect x="632" y="110" width="100" height="28" rx="8" fill="url(#primaryBtn)"/>
      <text x="682" y="128" font-family="Arial, sans-serif" font-size="13" font-weight="600" fill="white" text-anchor="middle">查看详情</text>
    </g>
    
    <!-- 历史面试记录区域 -->
    <g transform="translate(0, 248)">
      <!-- 区域标题 -->
      <text x="0" y="24" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#1e293b">历史面试记录</text>
      
      <!-- 面试卡片1 -->
      <rect x="0" y="40" width="752" height="100" rx="16" fill="#f9f9f9" filter="url(#cardShadow)"/>
      
      <!-- 面试信息 -->
      <text x="20" y="70" font-family="Arial, sans-serif" font-size="17" font-weight="600" fill="#1e293b">前端开发工程师面试</text>
      <text x="20" y="90" font-family="Arial, sans-serif" font-size="13" fill="#64748b">2024-01-15 14:30---15:45</text>
      
      <!-- 右侧评分和统计 -->
      <g transform="translate(580, 60)">
        <!-- 评分圆圈 -->
        <circle cx="25" cy="25" r="25" fill="url(#excellentScore)"/>
        <text x="25" y="32" font-family="Arial, sans-serif" font-size="16" font-weight="700" fill="white" text-anchor="middle">85</text>
        <text x="25" y="50" font-family="Arial, sans-serif" font-size="11" fill="#64748b" text-anchor="middle">综合评分</text>
        
        <!-- 快速统计 -->
        <g transform="translate(70, 10)">
          <text x="0" y="0" font-family="Arial, sans-serif" font-size="13" font-weight="700" fill="#1e293b">2156</text>
          <text x="0" y="15" font-family="Arial, sans-serif" font-size="10" fill="#64748b">转写字数</text>
          
          <text x="50" y="0" font-family="Arial, sans-serif" font-size="13" font-weight="700" fill="#1e293b">12</text>
          <text x="50" y="15" font-family="Arial, sans-serif" font-size="10" fill="#64748b">问题</text>
        </g>
      </g>
      
      <!-- 查看详情按钮 -->
      <rect x="632" y="110" width="100" height="28" rx="8" fill="url(#primaryBtn)"/>
      <text x="682" y="128" font-family="Arial, sans-serif" font-size="13" font-weight="600" fill="white" text-anchor="middle">查看详情</text>
      
      <!-- 面试卡片2 -->
      <rect x="0" y="160" width="752" height="100" rx="16" fill="#f9f9f9" filter="url(#cardShadow)"/>
      
      <!-- 面试信息 -->
      <text x="20" y="190" font-family="Arial, sans-serif" font-size="17" font-weight="600" fill="#1e293b">后端开发工程师面试</text>
      <text x="20" y="210" font-family="Arial, sans-serif" font-size="13" fill="#64748b">2024-01-12 10:00---11:30</text>
      
      <!-- 右侧评分和统计 -->
      <g transform="translate(580, 180)">
        <!-- 评分圆圈 -->
        <circle cx="25" cy="25" r="25" fill="url(#goodScore)"/>
        <text x="25" y="32" font-family="Arial, sans-serif" font-size="16" font-weight="700" fill="white" text-anchor="middle">78</text>
        <text x="25" y="50" font-family="Arial, sans-serif" font-size="11" fill="#64748b" text-anchor="middle">综合评分</text>
        
        <!-- 快速统计 -->
        <g transform="translate(70, 10)">
          <text x="0" y="0" font-family="Arial, sans-serif" font-size="13" font-weight="700" fill="#1e293b">1890</text>
          <text x="0" y="15" font-family="Arial, sans-serif" font-size="10" fill="#64748b">转写字数</text>
          
          <text x="50" y="0" font-family="Arial, sans-serif" font-size="13" font-weight="700" fill="#1e293b">15</text>
          <text x="50" y="15" font-family="Arial, sans-serif" font-size="10" fill="#64748b">问题</text>
        </g>
      </g>
      
      <!-- 查看详情按钮 -->
      <rect x="632" y="230" width="100" height="28" rx="8" fill="url(#primaryBtn)"/>
      <text x="682" y="248" font-family="Arial, sans-serif" font-size="13" font-weight="600" fill="white" text-anchor="middle">查看详情</text>
    </g>
  </g>
</svg>
