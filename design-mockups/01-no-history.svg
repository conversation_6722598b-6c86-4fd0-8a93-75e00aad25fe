<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ecf0f3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9f9f9;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="8" dy="8" stdDeviation="8" flood-color="#d1d9e6" flood-opacity="0.8"/>
      <feDropShadow dx="-8" dy="-8" stdDeviation="8" flood-color="#ffffff" flood-opacity="0.8"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="url(#bgGradient)"/>
  
  <!-- 主容器 -->
  <g transform="translate(24, 24)">
    <!-- 页面标题 -->
    <text x="0" y="32" font-family="Arial, sans-serif" font-size="28" font-weight="700" fill="#1e293b">面试总结</text>
    <text x="0" y="56" font-family="Arial, sans-serif" font-size="16" fill="#64748b">查看和管理您的历史面试记录</text>
    
    <!-- 历史面试记录区域 -->
    <g transform="translate(0, 88)">
      <!-- 区域标题 -->
      <text x="0" y="24" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#1e293b">历史面试记录</text>
      
      <!-- 空状态容器 -->
      <g transform="translate(376, 120)">
        <!-- 空状态图标 -->
        <circle cx="0" cy="0" r="40" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="2"/>
        <path d="M-20,-10 L20,-10 L20,10 L-20,10 Z M-15,-5 L15,-5 M-15,0 L10,0 M-15,5 L12,5" 
              stroke="#94a3b8" stroke-width="2" fill="none"/>
        
        <!-- 空状态文字 -->
        <text x="0" y="80" font-family="Arial, sans-serif" font-size="16" fill="#94a3b8" text-anchor="middle">
          暂无历史面试记录
        </text>
      </g>
    </g>
  </g>
  
  <!-- 装饰元素 -->
  <circle cx="720" cy="80" r="3" fill="#4B70E2" opacity="0.3"/>
  <circle cx="740" cy="120" r="2" fill="#4B70E2" opacity="0.2"/>
  <circle cx="760" cy="100" r="1.5" fill="#4B70E2" opacity="0.4"/>
</svg>
