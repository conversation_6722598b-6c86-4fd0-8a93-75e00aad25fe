## OfferHub

基于 Electron + Vue 3 + Vite 构建的桌面端应用脚手架，集成 Element Plus 与 Vue Router，支持开发时热重载与一键打包发布。默认提供无边框主窗口与常用窗口控制 IPC 事件，适合作为桌面应用的起步项目。


### 功能特性
- **技术栈**：Vue 3、Vite 5、Vue Router 4、Element Plus、Electron 29
- **开发体验**：一键启动前端 + 主进程，热更新生效
- **桌面特性**：无边框窗口、窗口最小化/最大化/关闭、登录后窗口尺寸切换
- **安全预加载**：通过 `preload` 以 `contextIsolation: true` 暴露受控的 `electronAPI`

### 目录结构
```text
offerhub/
├─ electron/                 # Electron 主/预加载脚本（开发源文件）
│  ├─ main.js
│  └─ preload.js
├─ src/                      # 渲染进程（前端）
│  ├─ main.js
│  ├─ App.vue
│  ├─ router/
│  │  └─ index.js
│  └─ components/
│     ├─ login/
│     ├─ option/
│     ├─ interview/
│     ├─ money/
│     └─ HomeNested/
├─ public/                   # 静态资源（图标等）
├─ dist-electron/            # 插件构建后的主/预加载产物（开发/构建生成）
├─ index.html                # Vite HTML 模板
├─ vite.config.js            # Vite 与 vite-plugin-electron 配置
└─ package.json
```

### 环境要求
- Node.js >= 18（Vite 5 要求）
- npm（或可改用 pnpm/yarn，自行调整命令）

### 安装依赖
```bash
npm install
```

### 开发调试（Electron + 前端热更新）
```bash
# 由 vite-plugin-electron 启动 Electron（推荐）
npm run start
```
运行后 Vite 启动，插件会自动拉起 Electron 并加载开发地址（默认 5173），改动渲染层代码实时生效。

可选脚本：
```bash
# 仅启动前端（浏览器访问，用于纯前端调试）
npm run dev

# 等待 5173 启动后仅启动 Electron（通常与 dev 配合）
npm run electron:serve
```

### 构建与打包
```bash
# 构建渲染进程（仅网页产物）
npm run build

# 构建渲染进程 + 使用 electron-builder 打包为安装包
npm run electron:build
```
默认打包产物位于 `release/` 目录，目标：
- Windows：NSIS 安装包


### 常用 npm 脚本
```jsonc
{
  "start": "vite",
  "dev": "vite",
  "build": "vite build",
  "preview": "vite preview",
  "electron:serve": "wait-on tcp:5173 && electron .",
  "electron:build": "vite build && electron-builder",
  "electron:start": "npm run build && electron ." // 离线本地运行已构建产物
}
```

### 窗口与 IPC 事件
主进程（`electron/main.js`）创建无边框窗口并监听以下事件：
- `close-app`：退出应用
- `minimize-window`：最小化窗口
- `maximize-window`：切换最大化/还原
- `login-success`：登录成功后将窗口调整为 1440x1080 并可调整大小
- `logout-reset`：退出登录后恢复到 980x580 不可调整大小

预加载（`electron/preload.js`）通过 `contextBridge` 暴露 `window.electronAPI`：
- `closeApp()`、`minimizeWindow()`、`maximizeWindow()`、`loginSuccess()`、`logoutReset()`
- `isElectron()`：用于在渲染进程中判断是否运行在 Electron 环境

### 路由与 UI
- 路由：`/login` 为默认入口，其余页面在 `router/option.js` 中扩展。
- UI：Element Plus，已启用中文本地化。

### 故障排查
- 启动空白窗口：请使用 `npm run start` 同时启动前端与 Electron；确认 5173 端口可达。
- Windows 下首次运行提示未知发布者：如需分发给外部用户，建议签名或在系统中放行。
- 预加载/隔离：已启用 `contextIsolation: true`，请通过 `window.electronAPI` 调用主进程功能，避免直接启用 `nodeIntegration`。


### 致谢
- Vite 与 `vite-plugin-electron`
- Vue 3、Vue Router、Element Plus

